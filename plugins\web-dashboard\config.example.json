{"comment": "Add this plugin configuration to your main SquadJS config.json plugins array", "plugin_configuration": {"plugin": "web-dashboard", "disabled": false, "options": {"dashboardApiUrl": "https://0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev", "apiTimeout": 5000, "serverId": 1, "enableDebugLogging": false, "retryAttempts": 3}}, "complete_example": {"comment": "Example of complete SquadJS config.json with this plugin", "server": {"id": 1, "host": "127.0.0.1", "queryPort": 27165, "rconPort": 21114, "rconPassword": "password", "logReaderMode": "tail", "logDir": "./path/to/squad/logs", "ftp": {"port": 21, "user": "FTP_USER", "password": "FTP_PASSWORD"}}, "connectors": {}, "plugins": [{"plugin": "web-dashboard", "disabled": false, "options": {"dashboardApiUrl": "https://0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev", "apiTimeout": 5000, "serverId": 1, "enableDebugLogging": false, "retryAttempts": 3}}]}, "configuration_options": {"dashboardApiUrl": {"description": "Base URL of your dashboard API server", "required": true, "default": "http://localhost:5000", "examples": ["https://0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev", "http://localhost:5000", "https://yourdomain.com"]}, "apiTimeout": {"description": "HTTP request timeout in milliseconds", "required": false, "default": 5000, "examples": [3000, 5000, 10000]}, "serverId": {"description": "Unique identifier for this server (useful for multi-server setups)", "required": false, "default": 1, "examples": [1, 2, 3]}, "enableDebugLogging": {"description": "Enable detailed logging for troubleshooting", "required": false, "default": false, "examples": [true, false]}, "retryAttempts": {"description": "Number of retry attempts for failed API calls", "required": false, "default": 3, "examples": [0, 3, 5]}}, "setup_instructions": ["1. Copy the web-dashboard folder to your SquadJS plugins directory", "2. Run 'npm install' in the web-dashboard plugin folder to install dependencies", "3. Start your dashboard API server (the backend we built)", "4. Add the plugin configuration to your SquadJS config.json plugins array", "5. Restart SquadJS to load the plugin", "6. Monitor SquadJS logs to verify the plugin is connecting to your dashboard API"]}