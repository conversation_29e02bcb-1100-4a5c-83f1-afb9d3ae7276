import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skull, Heart, UserMinus, UserPlus } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";

interface KillFeedEvent {
  id: string;
  type: "kill" | "death" | "revive" | "join" | "leave";
  timestamp: string;
  actor?: string;
  target?: string;
  weapon?: string;
  headshot?: boolean;
}

interface KillFeedProps {
  events: KillFeedEvent[];
  maxEvents?: number;
}

export default function KillFeed({ events, maxEvents = 20 }: KillFeedProps) {
  const getEventIcon = (type: string) => {
    switch (type) {
      case "kill": return <Skull className="w-3 h-3 text-red-500" />;
      case "revive": return <Heart className="w-3 h-3 text-green-500" />;
      case "join": return <UserPlus className="w-3 h-3 text-blue-500" />;
      case "leave": return <UserMinus className="w-3 h-3 text-gray-500" />;
      default: return <Skull className="w-3 h-3" />;
    }
  };

  const formatEvent = (event: KillFeedEvent) => {
    switch (event.type) {
      case "kill":
        return (
          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">{event.actor}</span>
            <span className="text-muted-foreground">→</span>
            <span>{event.target}</span>
            {event.weapon && (
              <Badge variant="outline" className="text-xs">
                {event.weapon}
              </Badge>
            )}
            {event.headshot && (
              <Badge variant="destructive" className="text-xs">
                HS
              </Badge>
            )}
          </div>
        );
      case "revive":
        return (
          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">{event.actor}</span>
            <span className="text-muted-foreground">revived</span>
            <span>{event.target}</span>
          </div>
        );
      case "join":
        return (
          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">{event.actor}</span>
            <span className="text-muted-foreground">joined the server</span>
          </div>
        );
      case "leave":
        return (
          <div className="flex items-center gap-2 text-sm">
            <span className="font-medium">{event.actor}</span>
            <span className="text-muted-foreground">left the server</span>
          </div>
        );
      default:
        return <span className="text-sm text-muted-foreground">Unknown event</span>;
    }
  };

  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('en-US', { 
      hour12: false, 
      hour: '2-digit', 
      minute: '2-digit', 
      second: '2-digit' 
    });
  };

  const displayEvents = events.slice(0, maxEvents);

  return (
    <Card data-testid="card-kill-feed" className="hover-elevate">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          <Skull className="w-5 h-5" />
          Live Feed
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-96">
          <div className="space-y-2">
            {displayEvents.map((event) => (
              <div
                key={event.id}
                data-testid={`event-${event.id}`}
                className="flex items-start gap-3 p-2 rounded-md hover-elevate border-l-2 border-muted"
              >
                <div className="flex-shrink-0 mt-1">
                  {getEventIcon(event.type)}
                </div>
                <div className="flex-1 min-w-0">
                  {formatEvent(event)}
                  <p className="text-xs text-muted-foreground font-mono mt-1">
                    {formatTime(event.timestamp)}
                  </p>
                </div>
              </div>
            ))}
            {displayEvents.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <Skull className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p>No recent events</p>
              </div>
            )}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}