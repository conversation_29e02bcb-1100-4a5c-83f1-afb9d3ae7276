import { useState } from "react";
import { useQuery, useMutation } from "@tanstack/react-query";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Search, Filter, RefreshCw } from "lucide-react";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useSocket } from "@/hooks/useSocket";

import ServerHeader from "@/components/ServerHeader";
import StatsCard from "@/components/StatsCard";
import PlayerCard from "@/components/PlayerCard";
import Leaderboard from "@/components/Leaderboard";
import KillFeed from "@/components/KillFeed";

// Type definitions for API responses
interface Player {
  steamId: string;
  name: string;
  eosId?: string;
  kills: number;
  deaths: number;
  revives: number;
  status: "online" | "offline";
  lastSeen: string;
  squad?: string;
  role?: string;
}

interface ServerInfo {
  name: string;
  currentMap: string;
  playerCount: number;
  maxPlayers: number;
  status: string;
  lastUpdate: string;
  uptime?: string;
}

interface LeaderboardEntry {
  rank: number;
  steamId: string;
  name: string;
  value: number;
}

interface Leaderboard {
  metric: string;
  period: string;
  entries: LeaderboardEntry[];
  lastUpdated: string;
}

interface DashboardSummary {
  activePlayers: number;
  maxPlayers: number;
  totalKills24h: number;
  totalRevives24h: number;
  serverUptime: string;
  topKillers: LeaderboardEntry[];
  topMedics: LeaderboardEntry[];
  recentEvents: any[];
}

interface GameEvent {
  id: string | number;
  type: string;
  timestamp: string;
  actor?: string;
  target?: string;
  meta?: {
    weapon?: string;
    headshot?: boolean;
    [key: string]: any;
  };
}

interface KillFeedEvent {
  id: string;
  type: "kill" | "death" | "revive" | "join" | "leave";
  timestamp: string;
  actor?: string;
  target?: string;
  weapon?: string;
  headshot?: boolean;
}

export default function Dashboard() {
  const [searchTerm, setSearchTerm] = useState("");
  
  // Initialize WebSocket connection for real-time updates
  useSocket();

  // Fetch server information
  const { data: serverInfo, isLoading: serverLoading } = useQuery<ServerInfo>({
    queryKey: ["/api/server"],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Fetch players list
  const { data: players = [], isLoading: playersLoading, refetch: refetchPlayers } = useQuery<Player[]>({
    queryKey: ["/api/players"],
    refetchInterval: 15000, // Refresh every 15 seconds
  });

  // Fetch leaderboards
  const { data: killsLeaderboard, isLoading: killsLoading } = useQuery<Leaderboard>({
    queryKey: ["/api/leaderboard/kills/week"],
    refetchInterval: 60000, // Refresh every minute
  });

  const { data: revivesLeaderboard, isLoading: revivesLoading } = useQuery<Leaderboard>({
    queryKey: ["/api/leaderboard/revives/week"],
    refetchInterval: 60000, // Refresh every minute
  });

  // Fetch recent events
  const { data: events = [], isLoading: eventsLoading, refetch: refetchEvents } = useQuery<GameEvent[]>({
    queryKey: ["/api/events"],
    refetchInterval: 10000, // Refresh every 10 seconds
  });

  // Dashboard summary for stats cards
  const { data: dashboardSummary, isLoading: summaryLoading, refetch: refetchSummary } = useQuery<DashboardSummary>({
    queryKey: ["/api/dashboard/summary"],
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  // Filter players based on search term
  const filteredPlayers = players.filter((player) =>
    player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    player.steamId.includes(searchTerm)
  );

  // Refresh all data mutation
  const refreshMutation = useMutation({
    mutationFn: async () => {
      // Invalidate all queries to force refresh
      await queryClient.invalidateQueries({ queryKey: ["/api/server"] });
      await queryClient.invalidateQueries({ queryKey: ["/api/players"] });
      await queryClient.invalidateQueries({ queryKey: ["/api/leaderboard"] });
      await queryClient.invalidateQueries({ queryKey: ["/api/events"] });
      await queryClient.invalidateQueries({ queryKey: ["/api/dashboard/summary"] });
      return "refreshed";
    },
    onSuccess: () => {
      console.log('Dashboard refreshed successfully');
    }
  });

  const handleRefresh = () => {
    refreshMutation.mutate();
  };

  const handlePlayerSteamLink = (steamId: string) => {
    console.log(`Opening Steam profile for ${steamId}`);
    // todo: implement Steam profile opening
  };

  const handlePlayerStats = (steamId: string) => {
    console.log(`Viewing detailed stats for ${steamId}`);
    // todo: implement detailed stats modal
  };

  return (
    <div className="space-y-6 p-6">
      {/* Server Header */}
      <div className="flex items-center justify-between">
        <ServerHeader
          name={serverInfo?.name || "Squad Server"}
          map={serverInfo?.currentMap || "Unknown Map"}
          playersCount={serverInfo?.playerCount || 0}
          maxPlayers={serverInfo?.maxPlayers || 96}
          status={(serverInfo?.status === "online" || serverInfo?.status === "offline") ? serverInfo.status : "offline"}
          lastUpdate={serverInfo?.lastUpdate ? new Date(serverInfo.lastUpdate).toLocaleTimeString() : "Unknown"}
        />
        <Button
          variant="outline"
          size="icon"
          onClick={handleRefresh}
          data-testid="button-refresh-dashboard"
        >
          <RefreshCw className="w-4 h-4" />
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatsCard
          title="Active Players"
          value={dashboardSummary?.activePlayers || 0}
          subtitle={`of ${dashboardSummary?.maxPlayers || 96} max`}
          trend="up"
          trendValue="+5"
          variant="positive"
        />
        <StatsCard
          title="Total Kills"
          value={dashboardSummary?.totalKills24h || 0}
          subtitle="last 24h" 
          trend="stable"
          trendValue="0%"
        />
        <StatsCard
          title="Revives"
          value={dashboardSummary?.totalRevives24h || 0}
          subtitle="last 24h"
          trend="up"
          trendValue="+12%"
          variant="positive"
        />
        <StatsCard
          title="Server Uptime"
          value={dashboardSummary?.serverUptime || "98.5%"}
          subtitle="last 30 days"
          trend="down"
          trendValue="-0.3%"
          variant="neutral"
        />
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="players" className="space-y-4">
        <TabsList data-testid="tabs-dashboard-main">
          <TabsTrigger value="players" data-testid="tab-players">Players</TabsTrigger>
          <TabsTrigger value="leaderboards" data-testid="tab-leaderboards">Leaderboards</TabsTrigger>
          <TabsTrigger value="events" data-testid="tab-events">Live Events</TabsTrigger>
        </TabsList>

        <TabsContent value="players" className="space-y-4">
          <div className="flex items-center gap-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
              <Input
                placeholder="Search players by name or Steam ID..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
                data-testid="input-player-search"
              />
            </div>
            <Button variant="outline" data-testid="button-filter-players">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>

          {playersLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading players...
            </div>
          ) : (
            <div className="space-y-3">
              {filteredPlayers.map((player) => (
                <PlayerCard
                  key={player.steamId}
                  name={player.name}
                  steamId={player.steamId}
                  squad={player.squad || "Unknown"}
                  role={player.role || "Unknown"}
                  score={player.kills * 10} // Calculate score based on kills for now
                  kills={player.kills}
                  deaths={player.deaths}
                  revives={player.revives}
                  status={player.status}
                  onSteamLink={() => handlePlayerSteamLink(player.steamId)}
                  onViewStats={() => handlePlayerStats(player.steamId)}
                />
              ))}
              {filteredPlayers.length === 0 && !playersLoading && (
                <div className="text-center py-8 text-muted-foreground">
                  No players found matching your search.
                </div>
              )}
            </div>
          )}
        </TabsContent>

        <TabsContent value="leaderboards" className="space-y-6">
          {killsLoading || revivesLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading leaderboards...
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Leaderboard
                title="Top Killers"
                entries={killsLeaderboard?.entries || []}
                metric="kills"
                period="week"
              />
              <Leaderboard
                title="Best Medics"
                entries={revivesLeaderboard?.entries || []}
                metric="revives"
                period="week"
              />
            </div>
          )}
        </TabsContent>

        <TabsContent value="events">
          {eventsLoading ? (
            <div className="text-center py-8 text-muted-foreground">
              Loading events...
            </div>
          ) : (
            <KillFeed events={events.map(event => ({
              id: String(event.id),
              type: event.type as "kill" | "death" | "revive" | "join" | "leave",
              timestamp: event.timestamp,
              actor: event.actor,
              target: event.target,
              weapon: event.meta?.weapon,
              headshot: event.meta?.headshot
            }))} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}