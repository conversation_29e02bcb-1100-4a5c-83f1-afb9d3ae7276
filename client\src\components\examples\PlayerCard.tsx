import PlayerCard from '../PlayerCard';

export default function PlayerCardExample() {
  return (
    <div className="space-y-4">
      <PlayerCard
        name="KING"
        steamId="76561199585954249"
        squad="Alpha-1"
        role="Rifleman"
        score={1200}
        kills={15}
        deaths={8}
        revives={5}
        status="online"
        onSteamLink={() => console.log('Steam link clicked')}
        onViewStats={() => console.log('View stats clicked')}
      />
      <PlayerCard
        name="GoldenEagle"
        steamId="76561198123456789"
        squad="Bravo-2"
        role="Medic"
        score={950}
        kills={8}
        deaths={12}
        revives={18}
        status="offline"
        onSteamLink={() => console.log('Steam link clicked')}
        onViewStats={() => console.log('View stats clicked')}
      />
    </div>
  );
}