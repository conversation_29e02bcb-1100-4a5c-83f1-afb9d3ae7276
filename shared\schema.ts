import { sql } from "drizzle-orm";
import { 
  pgTable, 
  text, 
  varchar, 
  integer, 
  timestamp, 
  json, 
  index,
  pgEnum,
  unique
} from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// SquadJS Event Types
export const eventTypeEnum = pgEnum("event_type", [
  "kill", 
  "death", 
  "revive", 
  "join", 
  "leave", 
  "respawn", 
  "round_start", 
  "round_end"
]);

export const periodTypeEnum = pgEnum("period_type", [
  "all",
  "week", 
  "month"
]);

// Player table - tracks unique players by Steam ID
export const players = pgTable("players", {
  id: integer("id").primaryKey().generatedByDefaultAsIdentity(),
  steamId64: varchar("steam_id_64", { length: 17 }).notNull().unique(),
  eosId: varchar("eos_id", { length: 32 }),
  displayName: varchar("display_name", { length: 100 }).notNull(),
  firstSeenAt: timestamp("first_seen_at").notNull().defaultNow(),
  lastSeenAt: timestamp("last_seen_at").notNull().defaultNow(),
}, (table) => ({
  steamIdIdx: index("players_steam_id_idx").on(table.steamId64),
  lastSeenIdx: index("players_last_seen_idx").on(table.lastSeenAt),
}));

// Event log - all game events with metadata
export const events = pgTable("events", {
  id: integer("id").primaryKey().generatedByDefaultAsIdentity(),
  type: eventTypeEnum("type").notNull(),
  timestamp: timestamp("timestamp").notNull().defaultNow(),
  serverId: integer("server_id"),
  actorSteamId: varchar("actor_steam_id", { length: 17 }),
  targetSteamId: varchar("target_steam_id", { length: 17 }),
  meta: json("meta").$type<{
    weapon?: string;
    headshot?: boolean;
    map?: string;
    squad?: string;
    location?: { x: number; y: number; z: number };
    damage?: number;
  }>(),
}, (table) => ({
  typeTimestampIdx: index("events_type_timestamp_idx").on(table.type, table.timestamp),
  actorIdx: index("events_actor_idx").on(table.actorSteamId),
  targetIdx: index("events_target_idx").on(table.targetSteamId),
  timestampIdx: index("events_timestamp_idx").on(table.timestamp),
  serverIdIdx: index("events_server_id_idx").on(table.serverId),
}));

// Player stats cache for faster leaderboard queries
export const playerStats = pgTable("player_stats", {
  id: integer("id").primaryKey().generatedByDefaultAsIdentity(),
  playerId: integer("player_id").notNull().references(() => players.id),
  periodType: periodTypeEnum("period_type").notNull(),
  since: timestamp("since").notNull(),
  kills: integer("kills").notNull().default(0),
  deaths: integer("deaths").notNull().default(0),
  revives: integer("revives").notNull().default(0),
  revived: integer("revived").notNull().default(0),
  updatedAt: timestamp("updated_at").notNull().defaultNow(),
}, (table) => ({
  playerPeriodIdx: index("player_stats_player_period_idx").on(table.playerId, table.periodType),
  sinceIdx: index("player_stats_since_idx").on(table.since),
  playerPeriodUnique: unique("player_stats_player_period_unique").on(table.playerId, table.periodType),
}));

// Steam account linking
export const steamLinks = pgTable("steam_links", {
  id: integer("id").primaryKey().generatedByDefaultAsIdentity(),
  steamId64: varchar("steam_id_64", { length: 17 }).notNull().unique(),
  pluginUserId: varchar("plugin_user_id", { length: 100 }),
  linkedAt: timestamp("linked_at").notNull().defaultNow(),
}, (table) => ({
  steamIdIdx: index("steam_links_steam_id_idx").on(table.steamId64),
  userIdIdx: index("steam_links_user_id_idx").on(table.pluginUserId),
}));

// Legacy user table (keeping for auth integration)
export const users = pgTable("users", {
  id: varchar("id").primaryKey().default(sql`gen_random_uuid()`),
  username: text("username").notNull().unique(),
  password: text("password").notNull(),
});

// Zod schemas for validation
export const insertPlayerSchema = createInsertSchema(players).omit({
  id: true,
  firstSeenAt: true,
  lastSeenAt: true,
});

export const insertEventSchema = createInsertSchema(events).omit({
  id: true,
  timestamp: true,
});

export const insertPlayerStatsSchema = createInsertSchema(playerStats).omit({
  id: true,
  updatedAt: true,
});

export const insertSteamLinkSchema = createInsertSchema(steamLinks).omit({
  id: true,
  linkedAt: true,
});

export const insertUserSchema = createInsertSchema(users).pick({
  username: true,
  password: true,
});

// Type exports
export type Player = typeof players.$inferSelect;
export type InsertPlayer = z.infer<typeof insertPlayerSchema>;

export type Event = typeof events.$inferSelect;
export type InsertEvent = z.infer<typeof insertEventSchema>;

export type PlayerStats = typeof playerStats.$inferSelect;
export type InsertPlayerStats = z.infer<typeof insertPlayerStatsSchema>;

export type SteamLink = typeof steamLinks.$inferSelect;
export type InsertSteamLink = z.infer<typeof insertSteamLinkSchema>;

export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
