ull, precise specification for a SquadJS plugin + web+discord+DB integration

Project / Plugin name: squadjs-web-dashboard (internal plugin id: web-dashboard)

Goal (short):
Create a SquadJS plugin that listens to in-game events (kills, deaths, revives, joins, leaves, round start/end), stores those events to a SQL DB (via Sequelize), emits aggregated leaderboards (top 10 weekly/monthly), and publishes live server+player state to:

A web dashboard (REST API + optional WebSocket).

A Discord channel via the existing Discord connector.

The web dashboard must show: server name, player count, player list (with SteamID links), per-player stats (all-time, week, month), and update automatically (every 30s) via WebSocket or polling.

High-level constraints & environment

Must be a SquadJS plugin conforming to the repo plugin model (declared in config.json and installed in plugins/).

Use Node.js (same LTS as SquadJS) and Sequelize for DB (support mysql or sqlite as configured via config.json connectors). SquadJS already supports connectors for Discord and Sequelize. 
GitHub

Plugin must be configurable via config.json plugin options (webhook URL, discord client name, channel ID, DB connector name, polling frequency, CORS, auth secrets).

Provide Docker-friendly environment variables for production.

Core responsibilities

Event ingestion

Subscribe to SquadJS events (log parser events). For each relevant event, normalize and insert rows in DB.

Events to capture: player:join, player:leave, player:kill, player:death, player:revive, player:respawn, round:start, round:end, server:info (name/rotation).

Note: If exact SquadJS internal event names differ, map the log-parser output to these canonical names inside the plugin; verify event names in codebase.

Database (persistent)

Create Sequelize models / migrations (see schema below).

Ensure indices on timestamp fields and SteamID for fast leaderboard queries.

Web API & Real-time

Provide a small Express (or integrate with SquadJS http if available) API that serves server info, players, and stats.

Provide a WebSocket (socket.io) endpoint for clients to connect to receive live updates (server state broadcast every 30s; player join/leave/kill events pushed immediately).

Discord

Use the configured Discord connector name from SquadJS config (e.g., discord) and send messages to channelIDs specified in plugin config.

For kills/revives/round end create nicely formatted embeds (or short messages). Configurable toggles: discord.sendKills, discord.sendRevives, discord.sendServerSummary.

Linking Steam account with plugin account

Provide an API endpoint POST /link/start that returns a Steam OpenID redirect URL. After OpenID callback, map SteamID64 to user in SteamLinks table.

Provide GET /me endpoint that returns linked SteamID data (requires auth token — see security section).

Leaderboards & Aggregation

Periodic job (cron or setInterval) that computes top 10 for categories (kills, K/D, revives, revives_received) for intervals: week (last 7 days), month (last 30 days), and all-time.

Store computed leaderboards in DB cache table to speed up API responses; refresh every 5 minutes or on-demand.

Export / Webhook

Allow pushing aggregated data to external web service via configurable webhook webhook.url with an HMAC signature header.

The plugin will also be able to post raw events or batched snapshots.

Config options (example config.json plugin block)
{
  "plugin": "web-dashboard",
  "disabled": false,
  "options": {
    "web": {
      "enabled": true,
      "port": 3001,
      "basePath": "/api",
      "authSecret": "ENV_OR_CONFIG_SECRET",
      "cors": ["https://your-site.example"],
      "snapshotIntervalSeconds": 30
    },
    "discord": {
      "connector": "discord",
      "sendKills": true,
      "sendRevives": false,
      "channelID": "123456789012345678",
      "embedColor": 16761867
    },
    "database": {
      "connector": "mysql"        // name of Sequelize connector in the global config
    },
    "webhook": {
      "url": "https://example.com/receive-squad",
      "enabled": true,
      "hmacSecret": "ENV_OR_CONFIG_SECRET"
    },
    "leaderboard": {
      "refreshIntervalSeconds": 300
    }
  }
}

Suggested Sequelize models (JS pseudocode)

(Use migrations; include createdAt timestamps)

Player

Player = sequelize.define('Player', {
  id: { type: INTEGER, primaryKey:true, autoIncrement:true },
  steamId64: { type: STRING, unique: true, allowNull: false },
  eosId: { type: STRING, allowNull: true },
  displayName: { type: STRING, allowNull:false },
  firstSeenAt: { type: DATE },
  lastSeenAt: { type: DATE }
});


Event (generic event log)

Event = sequelize.define('Event', {
  id: { type: INTEGER, primaryKey:true, autoIncrement:true },
  type: { type: ENUM('kill','death','revive','join','leave','respawn','round_start','round_end'), allowNull:false },
  timestamp: { type: DATE, allowNull:false },
  serverId: { type: INTEGER },
  actorSteamId: { type: STRING },   // who performed action (killer/reviver)
  targetSteamId: { type: STRING },  // who got killed/revived
  meta: { type: JSON }              // payload (weapon, map, squad, location)
});
Event.index('type_timestamp', { fields: ['type','timestamp'] });
Event.index('actor_idx', { fields: ['actorSteamId'] });
Event.index('target_idx', { fields: ['targetSteamId'] });


KillSummary / Stats (optional denormalized cache)

PlayerStats = sequelize.define('PlayerStats', {
  playerId: INTEGER,
  periodType: ENUM('all','week','month'),
  since: DATE,
  kills: INTEGER,
  deaths: INTEGER,
  revives: INTEGER,
  revived: INTEGER,
  // store ranking if needed
});


SteamLinks

SteamLink = sequelize.define('SteamLink', {
  id: auto,
  steamId64: STRING,
  pluginUserId: STRING, // identifier in plugin (or in your site DB)
  linkedAt: DATE
});

API endpoints (HTTP)

(All endpoints are under /api by default — require an API key or JWT for sensitive routes.)

Public / read-only

GET /api/server
Returns server name, map, maxPlayers, currentPlayersCount, rotation, lastSnapshotAt.

GET /api/players
Returns list of current players: {name, steamId64, squad, role, score,...}

GET /api/player/:steamId
Returns aggregated stats (all-time/week/month), favorites, top victims/nemeses (similar to your example output).

GET /api/leaderboard/:metric/:period
Example: /api/leaderboard/kills/week returns top 10.

Auth-required

POST /api/link/steam/start
Returns Steam OpenID URL for the client to redirect to.

GET /api/link/steam/callback
Steam OpenID callback that creates the SteamLink entry and returns a short-lived token or redirect.

POST /api/webhook-test
For admin to test webhook HMAC signing.

WebSocket

ws://host:port/

On connect, client can subscribe to server_snapshot channel and receive snapshot every 30s, plus real-time events player:join/leave/kill/revive as they happen.

Example payloads

30s server snapshot (POST to webhook or WS message)

{
  "server": {
    "id": 1,
    "name": "My Squad Server",
    "map": "GE AlBasrah Invasion v7",
    "playersCount": 42,
    "maxPlayers": 96,
    "time": "2025-08-26T00:15:00Z"
  },
  "players": [{
    "name":"KING",
    "steamId":"76561199585954249",
    "squad":"Alpha-1",
    "role":"Rifleman",
    "score": 1200
  }...],
  "summary": {
    "kills_last_30s": 10,
    "revives_last_30s": 3
  }
}


Kill event (for Discord embed or webhook)

{
  "type":"kill",
  "timestamp":"2025-08-26T00:12:13Z",
  "serverId":1,
  "actor": { "name":"KING", "steamId":"76561199585954249" },
  "target": { "name":"GoldenEagle", "steamId":"7656119..." },
  "meta": { "weapon":"M4 SOPMOD", "headshot":false }
}

Leaderboard queries — examples (Sequelize pseudo)

Top 10 kills, week

const since = new Date(Date.now() - 7*24*3600*1000);
const rows = await Event.findAll({
  attributes: ['actorSteamId',[sequelize.fn('COUNT','id'),'kills']],
  where: { type: 'kill', timestamp: { [Op.gt]: since } },
  group: 'actorSteamId',
  order: [[sequelize.literal('kills'),'DESC']],
  limit: 10
});


K/D ratio (period)

Use aggregated counts of kills and deaths for the period; compute ratio and sort by ratio with minimum games threshold.

Discord messages (format)

Use discord.js embed with:

Title: Kill: KING → GoldenEagle

Fields: Weapon, Map, Time

Footer: server name + snapshot time

For top-10 weekly summary, post a single embed containing the ranked list. Make Discord posting toggles available in config.

Steam OpenID linking flow

POST /api/link/steam/start returns openid_redirect_url (built using Steam OpenID endpoints).

User completes Steam login; Steam redirects to /api/link/steam/callback?openid....

Plugin verifies OpenID response, extracts steamid (SteamID64) and stores in SteamLinks.

Generate a short JWT tied to the plugin user account for authenticated API calls.
Security: never accept arbitrary SteamID from client; always verify via OpenID callback.

Security & operational notes

Auth: allow an API key / JWT (rotateable) for admin endpoints. Public read endpoints can be unauthenticated or rate-limited.

Rate limiting: limit webhook calls and API endpoints (e.g. 10 req/sec per IP).

HMAC: sign outgoing webhooks using HMAC-SHA256 header X-Signature: sha256=....

Input validation: never insert raw log strings into DB without sanitization; rely on typed fields.

Data retention: provide retention policy for Event table (e.g. older than 180 days can be archived/aggregated/summarized).

DB indices: add composite indices for queries on type+timestamp, actorSteamId, targetSteamId.

Testing & validation

Unit tests for:

Event parsing/mapping from SquadJS log lines → canonical event object.

DB writes and aggregation queries.

API endpoints (mock DB).

Steam OpenID flow (integration test with Steam test account if possible).

Integration tests:

Simulate a stream of events and assert the snapshot pushed to WebSocket + Discord messages match expected shape.

Deliverables

plugins/web-dashboard/ directory source code: plugin entrypoint, service files, express server, socket.io, Sequelize models, migrations.

README.md with installation and config instructions (example config.json).

Example front-end (single-file React or Vue component) that connects to WS and renders server name, players list, and per-player modal with stats. The front-end should poll or subscribe and refresh every 30s. (Minimal UI is fine.)

Example docker-compose.yml service that starts plugin service + database (mysql) and documents env vars.

Unit + integration tests and migration scripts.

Implementation notes for the developer (practical)

Integrate into SquadJS plugin lifecycle (init, start, stop); use connectors for DB and Discord. SquadJS README shows connectors and Sequelize usage; adopt connector names from the global config. 
GitHub

If the repo already has DBLog or similar plugin (it does — which logs to DB), reuse its models/approach for event parsing to ensure consistency. (There are existing DB logging plugins; inspect mysql-log/DBLog for patterns). 
GitHub

Provide a small, well-documented mapping layer: squadLogLine -> canonicalEvent. That isolates changes if Squad log format changes.

Make frequency adjustable (snapshotIntervalSeconds default 30). For reliability, send snapshots on change + periodic snapshot.

Example small front-end spec (for the dashboard)

Single page app (React or Vue) with 3 panels:

Server Header (server name, map, playersCount).

Player List (scrollable); each row: avatar (Steam avatar via Steam Web API), name, steamID, squad, quick stats (kills/deaths), link-button Link Steam (if not linked).

Right pane: Selected player details — full stats, favorites, top nemesis/victim, recent events.

Auto-refresh: Use WebSocket for immediate events; fallback to polling /api/server every 30s.

Provide "Link Steam" button that opens Steam/OpenID flow via our plugin API.

Extra suggestions / optional features

Add caching layer (Redis) for heavy aggregation.

Add a background job to compute "best 10 of week/month" using windowed queries into Event table, triggered every 5 minutes.

Provide export endpoints (CSV of events).

Implement role-based admin UI for toggling Discord or webhooks.

Notes & follow-ups for you (what I could not 100% confirm)

I used the SquadJS README as the main reference for how connectors and DB integration are intended to work (Discord connector, Sequelize connector and plugin config). For exact method names and internal event names emitted by the core, the implementer should inspect the core and log parser modules in the repository and adapt the event listener names accordingly. (Repository README confirms the general architecture: live log parsing and connectors). 
GitHub
+1