import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import { Activity, Users, Globe, Clock } from "lucide-react";

interface ServerHeaderProps {
  name: string;
  map: string;
  playersCount: number;
  maxPlayers: number;
  status: "online" | "offline";
  lastUpdate?: string;
}

export default function ServerHeader({
  name,
  map,
  playersCount,
  maxPlayers,
  status,
  lastUpdate
}: ServerHeaderProps) {
  const statusColor = status === "online" ? "bg-green-500" : "bg-red-500";
  
  return (
    <Card data-testid="card-server-header" className="p-6 hover-elevate">
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-3">
          <div className={`w-3 h-3 rounded-full ${statusColor}`} data-testid="indicator-server-status" />
          <h1 className="text-2xl font-bold" data-testid="text-server-name">{name}</h1>
          <Badge variant="secondary" data-testid="badge-server-status">
            {status.toUpperCase()}
          </Badge>
        </div>
        
        <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
          <div className="flex items-center gap-2" data-testid="info-map">
            <Globe className="w-4 h-4" />
            <span>{map}</span>
          </div>
          
          <div className="flex items-center gap-2" data-testid="info-players">
            <Users className="w-4 h-4" />
            <span>{playersCount}/{maxPlayers}</span>
          </div>
          
          <div className="flex items-center gap-2" data-testid="info-activity">
            <Activity className="w-4 h-4" />
            <span>Live</span>
          </div>
          
          {lastUpdate && (
            <div className="flex items-center gap-2" data-testid="info-last-update">
              <Clock className="w-4 h-4" />
              <span>{lastUpdate}</span>
            </div>
          )}
        </div>
      </div>
    </Card>
  );
}