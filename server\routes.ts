import type { Express } from "express";
import { createServer, type Server } from "http";
import { Server as SocketIOServer } from "socket.io";
import { storage } from "./storage";
import { insertEventSchema, insertPlayerSchema, events } from "@shared/schema";
import { z } from "zod";
import { db } from "./db";
import { sql, desc } from "drizzle-orm";

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);
  
  // Initialize Socket.IO for real-time updates
  const io = new SocketIOServer(httpServer, {
    cors: {
      origin: "*",
      methods: ["GET", "POST"]
    }
  });

  // WebSocket connection handling
  io.on("connection", (socket) => {
    console.log("Client connected:", socket.id);
    
    socket.on("disconnect", () => {
      console.log("Client disconnected:", socket.id);
    });
  });

  // Helper function to broadcast updates to all connected clients
  const broadcastUpdate = (event: string, data: any) => {
    io.emit(event, data);
  };

  // GET /api/server - Server information including player count and current map
  app.get("/api/server", async (req, res) => {
    try {
      const activePlayers = await storage.getActivePlayers();
      const recentEvents = await storage.getRecentEvents(10);
      
      // Get latest map info from recent events
      const mapEvent = recentEvents.find(e => e.meta && 'map' in e.meta);
      const currentMap = mapEvent?.meta?.map || "Unknown Map";
      
      const serverInfo = {
        name: "Squad Server",
        currentMap,
        playerCount: activePlayers.length,
        maxPlayers: 96,
        status: "online" as const,
        lastUpdate: new Date().toISOString(),
        uptime: "98.5%"
      };
      
      res.json(serverInfo);
    } catch (error) {
      console.error("Error fetching server info:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // GET /api/players - List of active players with basic stats
  app.get("/api/players", async (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 100;
      const search = req.query.search as string;
      
      let players = await storage.getActivePlayers(limit);
      
      // Filter by search term if provided
      if (search) {
        const searchLower = search.toLowerCase();
        players = players.filter(p => 
          p.displayName.toLowerCase().includes(searchLower) ||
          p.steamId64.includes(search)
        );
      }
      
      // Get basic stats for each player
      const playersWithStats = await Promise.all(
        players.map(async (player) => {
          const stats = await storage.getPlayerStats(player.id, "all");
          return {
            steamId: player.steamId64,
            name: player.displayName,
            eosId: player.eosId,
            kills: stats?.kills || 0,
            deaths: stats?.deaths || 0,
            revives: stats?.revives || 0,
            status: isPlayerOnline(player.lastSeenAt) ? "online" : "offline",
            lastSeen: player.lastSeenAt,
            squad: "Unknown", // This would come from live game data
            role: "Unknown"   // This would come from live game data
          };
        })
      );
      
      res.json(playersWithStats);
    } catch (error) {
      console.error("Error fetching players:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // GET /api/player/:steamId - Individual player detailed stats
  app.get("/api/player/:steamId", async (req, res) => {
    try {
      const { steamId } = req.params;
      const player = await storage.getPlayerBySteamId(steamId);
      
      if (!player) {
        return res.status(404).json({ error: "Player not found" });
      }
      
      // Get stats for different periods
      const [allTimeStats, weekStats, monthStats] = await Promise.all([
        storage.getPlayerStats(player.id, "all"),
        storage.getPlayerStats(player.id, "week"),
        storage.getPlayerStats(player.id, "month")
      ]);
      
      // Get recent events for this player
      const recentEvents = await storage.getEventsByPlayer(steamId, 20);
      
      const playerDetails = {
        steamId: player.steamId64,
        eosId: player.eosId,
        name: player.displayName,
        firstSeen: player.firstSeenAt,
        lastSeen: player.lastSeenAt,
        status: isPlayerOnline(player.lastSeenAt) ? "online" : "offline",
        stats: {
          allTime: allTimeStats || { kills: 0, deaths: 0, revives: 0, revived: 0 },
          week: weekStats || { kills: 0, deaths: 0, revives: 0, revived: 0 },
          month: monthStats || { kills: 0, deaths: 0, revives: 0, revived: 0 }
        },
        recentEvents: recentEvents.map(event => ({
          id: event.id,
          type: event.type,
          timestamp: event.timestamp,
          meta: event.meta
        }))
      };
      
      res.json(playerDetails);
    } catch (error) {
      console.error("Error fetching player details:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // GET /api/leaderboard/:metric/:period - Leaderboards for kills/deaths/revives
  app.get("/api/leaderboard/:metric/:period", async (req, res) => {
    try {
      const { metric, period } = req.params;
      const limit = parseInt(req.query.limit as string) || 10;
      
      // Validate parameters
      if (!["kills", "deaths", "revives"].includes(metric)) {
        return res.status(400).json({ error: "Invalid metric. Must be kills, deaths, or revives" });
      }
      
      if (!["all", "week", "month"].includes(period)) {
        return res.status(400).json({ error: "Invalid period. Must be all, week, or month" });
      }
      
      const leaderboard = await storage.getLeaderboard(
        metric as "kills" | "deaths" | "revives",
        period as "all" | "week" | "month",
        limit
      );
      
      const formattedLeaderboard = leaderboard.map((entry, index) => ({
        rank: index + 1,
        steamId: entry.steamId,
        name: entry.player?.displayName || "Unknown Player",
        value: parseInt(entry.value) || 0
      }));
      
      res.json({
        metric,
        period,
        entries: formattedLeaderboard,
        lastUpdated: new Date().toISOString()
      });
    } catch (error) {
      console.error("Error fetching leaderboard:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // GET /api/events - Recent game events with filtering
  app.get("/api/events", async (req, res) => {
    try {
      const limit = parseInt(req.query.limit as string) || 50;
      const type = req.query.type as string;
      const steamId = req.query.steamId as string;
      
      let events;
      
      if (steamId) {
        events = await storage.getEventsByPlayer(steamId, limit);
      } else {
        events = await storage.getRecentEvents(limit);
      }
      
      // Filter by event type if specified
      if (type) {
        events = events.filter(e => e.type === type);
      }
      
      const formattedEvents = events.map(event => ({
        id: event.id,
        type: event.type,
        timestamp: event.timestamp,
        serverId: event.serverId,
        actor: event.actorSteamId,
        target: event.targetSteamId,
        meta: event.meta
      }));
      
      res.json(formattedEvents);
    } catch (error) {
      console.error("Error fetching events:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // POST /api/events - Endpoint for SquadJS to send game events
  app.post("/api/events", async (req, res) => {
    try {
      const eventData = insertEventSchema.parse(req.body);
      
      // Create the event
      const event = await storage.createEvent(eventData);
      
      // Broadcast the event to all connected clients
      broadcastUpdate("newEvent", {
        id: event.id,
        type: event.type,
        timestamp: event.timestamp,
        actor: event.actorSteamId,
        target: event.targetSteamId,
        meta: event.meta
      });
      
      // If this is a player join/leave event, update player data
      if (eventData.type === "join") {
        // Try multiple sources for Steam ID (robust fallback)
        const steamId = eventData.actorSteamId || req.body.actorSteamId || req.body.meta?.actorSteamId || req.body.meta?.steamId;
        const playerData = req.body.playerData;
        
        console.log(`[DEBUG] Join event processing:`, {
          type: eventData.type,
          steamId: steamId,
          hasPlayerData: !!playerData,
          playerData: playerData,
          reqBodyMeta: req.body.meta
        });
        
        if (steamId) {
          try {
            await storage.createOrUpdatePlayer({
              steamId64: steamId,
              displayName: playerData?.displayName || req.body.meta?.playerName || `Player_${steamId.slice(-6)}`,
              eosId: playerData?.eosId || req.body.meta?.eosId
            });
            console.log(`[DEBUG] Player created/updated: ${playerData?.displayName || req.body.meta?.playerName} (${steamId})`);
          } catch (playerError) {
            console.error(`[DEBUG] Error creating player:`, playerError);
          }
        } else {
          console.log(`[DEBUG] No Steam ID found in join event - skipping player creation`);
        }
      }
      
      res.status(201).json({ success: true, event });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid event data", details: error.errors });
      }
      console.error("Error creating event:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // POST /api/players - Endpoint for SquadJS to update player information
  app.post("/api/players", async (req, res) => {
    try {
      const playerData = insertPlayerSchema.parse(req.body);
      
      const player = await storage.createOrUpdatePlayer(playerData);
      
      // Broadcast player update
      broadcastUpdate("playerUpdate", {
        steamId: player.steamId64,
        name: player.displayName,
        status: "online",
        lastSeen: player.lastSeenAt
      });
      
      res.status(201).json({ success: true, player });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ error: "Invalid player data", details: error.errors });
      }
      console.error("Error updating player:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // POST /api/stats/update - Endpoint for updating player statistics
  app.post("/api/stats/update", async (req, res) => {
    try {
      const { steamId, period, stats } = req.body;
      
      if (!steamId || !period || !stats) {
        return res.status(400).json({ error: "Missing required fields: steamId, period, stats" });
      }
      
      const player = await storage.getPlayerBySteamId(steamId);
      if (!player) {
        return res.status(404).json({ error: "Player not found" });
      }
      
      // Calculate proper 'since' date based on period type
      let since: Date;
      const now = new Date();
      
      switch (period) {
        case "week":
          since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
          break;
        case "month":
          since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
          break;
        default:
          since = new Date(0); // All time
      }

      const updatedStats = await storage.updatePlayerStats({
        playerId: player.id,
        periodType: period,
        since,
        kills: stats.kills || 0,
        deaths: stats.deaths || 0,
        revives: stats.revives || 0,
        revived: stats.revived || 0
      });
      
      // Broadcast stats update
      broadcastUpdate("statsUpdate", {
        steamId,
        period,
        stats: updatedStats
      });
      
      res.json({ success: true, stats: updatedStats });
    } catch (error) {
      console.error("Error updating stats:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // GET /api/dashboard/summary - Summary data for dashboard overview
  app.get("/api/dashboard/summary", async (req, res) => {
    try {
      const [activePlayers, recentEvents, killsLeaderboard, revivesLeaderboard] = await Promise.all([
        storage.getActivePlayers(100),
        storage.getRecentEvents(100),
        storage.getLeaderboard("kills", "week", 5),
        storage.getLeaderboard("revives", "week", 5)
      ]);
      
      // Calculate summary stats
      const onlinePlayers = activePlayers.filter(p => isPlayerOnline(p.lastSeenAt));
      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const recent24hEvents = recentEvents.filter(e => e.timestamp >= last24Hours);
      
      const totalKills = recent24hEvents.filter(e => e.type === "kill").length;
      const totalRevives = recent24hEvents.filter(e => e.type === "revive").length;
      
      const summary = {
        activePlayers: onlinePlayers.length,
        maxPlayers: 96,
        totalKills24h: totalKills,
        totalRevives24h: totalRevives,
        serverUptime: "98.5%",
        topKillers: killsLeaderboard.slice(0, 5).map((entry, index) => ({
          rank: index + 1,
          name: entry.player?.displayName || "Unknown",
          value: parseInt(entry.value) || 0
        })),
        topMedics: revivesLeaderboard.slice(0, 5).map((entry, index) => ({
          rank: index + 1,
          name: entry.player?.displayName || "Unknown",
          value: parseInt(entry.value) || 0
        })),
        recentEvents: recent24hEvents.slice(0, 10).map(event => ({
          id: event.id,
          type: event.type,
          timestamp: event.timestamp,
          actor: event.actorSteamId,
          target: event.targetSteamId,
          meta: event.meta
        }))
      };
      
      res.json(summary);
    } catch (error) {
      console.error("Error fetching dashboard summary:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  });

  // Health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({ 
      status: "healthy", 
      timestamp: new Date().toISOString(),
      uptime: process.uptime()
    });
  });

  // Development endpoint to populate sample data
  app.post("/api/dev/populate-sample-data", async (req, res) => {
    try {
      // Create sample players
      const samplePlayers = [
        {
          steamId64: "76561199585954249",
          displayName: "KING",
          eosId: "eos_123456789"
        },
        {
          steamId64: "76561198123456789",
          displayName: "GoldenEagle",
          eosId: "eos_987654321"
        },
        {
          steamId64: "76561198987654321",
          displayName: "SniperElite",
          eosId: "eos_555666777"
        },
        {
          steamId64: "76561198111222333",
          displayName: "WarriorX",
          eosId: "eos_111222333"
        },
        {
          steamId64: "76561198444555666",
          displayName: "MedicsRule",
          eosId: "eos_444555666"
        },
        {
          steamId64: "76561198777888999",
          displayName: "LifeSaver",
          eosId: "eos_777888999"
        }
      ];

      // Insert players
      const createdPlayers = [];
      for (const player of samplePlayers) {
        const created = await storage.createOrUpdatePlayer(player);
        createdPlayers.push(created);
      }

      // Create sample events
      const sampleEvents = [
        {
          type: "join" as const,
          serverId: 1,
          actorSteamId: "76561199585954249",
          meta: { map: "GE AlBasrah Invasion v7" }
        },
        {
          type: "join" as const,
          serverId: 1,
          actorSteamId: "76561198123456789",
          meta: { map: "GE AlBasrah Invasion v7" }
        },
        {
          type: "kill" as const,
          serverId: 1,
          actorSteamId: "76561199585954249",
          targetSteamId: "76561198123456789",
          meta: { weapon: "M4 SOPMOD", headshot: true }
        },
        {
          type: "revive" as const,
          serverId: 1,
          actorSteamId: "76561198444555666",
          targetSteamId: "76561198123456789",
          meta: {}
        },
        {
          type: "kill" as const,
          serverId: 1,
          actorSteamId: "76561198987654321",
          targetSteamId: "76561198111222333",
          meta: { weapon: "SVD", headshot: true }
        }
      ];

      // Insert events
      const createdEvents = [];
      for (const event of sampleEvents) {
        const created = await storage.createEvent(event);
        createdEvents.push(created);
      }

      // Create sample stats
      for (const player of createdPlayers) {
        // All-time stats
        await storage.updatePlayerStats({
          playerId: player.id,
          periodType: "all",
          since: new Date(0), // All time
          kills: Math.floor(Math.random() * 50) + 10,
          deaths: Math.floor(Math.random() * 30) + 5,
          revives: Math.floor(Math.random() * 40) + 5,
          revived: Math.floor(Math.random() * 20) + 2
        });

        // Weekly stats
        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        await storage.updatePlayerStats({
          playerId: player.id,
          periodType: "week",
          since: weekAgo,
          kills: Math.floor(Math.random() * 20) + 5,
          deaths: Math.floor(Math.random() * 15) + 2,
          revives: Math.floor(Math.random() * 15) + 2,
          revived: Math.floor(Math.random() * 8) + 1
        });

        // Monthly stats
        const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        await storage.updatePlayerStats({
          playerId: player.id,
          periodType: "month",
          since: monthAgo,
          kills: Math.floor(Math.random() * 35) + 8,
          deaths: Math.floor(Math.random() * 25) + 3,
          revives: Math.floor(Math.random() * 25) + 3,
          revived: Math.floor(Math.random() * 12) + 1
        });
      }

      res.json({
        success: true,
        message: "Sample data populated successfully",
        data: {
          players: createdPlayers.length,
          events: createdEvents.length,
          statsEntries: createdPlayers.length * 3 // 3 periods per player
        }
      });
    } catch (error) {
      console.error("Error populating sample data:", error);
      res.status(500).json({ error: "Failed to populate sample data" });
    }
  });

  // Temporary endpoint to backfill missing players from join events
  app.post("/api/admin/backfill-players", async (req, res) => {
    try {
      // Get recent join events that have player data
      const joinEvents = await db
        .select()
        .from(events)
        .where(sql`${events.type} = 'join' AND ${events.actorSteamId} IS NOT NULL`)
        .orderBy(desc(events.id))
        .limit(50);

      let createdCount = 0;
      let skippedCount = 0;

      for (const event of joinEvents) {
        if (event.actorSteamId && event.meta?.playerName) {
          try {
            // Check if player already exists
            const existing = await storage.getPlayerBySteamId(event.actorSteamId);
            
            if (!existing) {
              await storage.createOrUpdatePlayer({
                steamId64: event.actorSteamId,
                displayName: event.meta.playerName.trim(),
                eosId: event.meta.eosId
              });
              createdCount++;
              console.log(`[BACKFILL] Created player: ${event.meta.playerName} (${event.actorSteamId})`);
            } else {
              skippedCount++;
            }
          } catch (playerError) {
            console.error(`[BACKFILL] Error creating player ${event.meta.playerName}:`, playerError);
          }
        }
      }

      res.json({
        success: true,
        message: `Backfill completed: ${createdCount} players created, ${skippedCount} skipped (already exist)`,
        createdCount,
        skippedCount
      });
    } catch (error) {
      console.error("Backfill error:", error);
      res.status(500).json({ error: "Backfill failed" });
    }
  });

  return httpServer;
}

// Helper function to determine if a player is considered online
function isPlayerOnline(lastSeenAt: Date): boolean {
  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
  return lastSeenAt > fiveMinutesAgo;
}

// Export the io instance for use in other parts of the application
export let socketIO: SocketIOServer;

export function setSocketIO(io: SocketIOServer) {
  socketIO = io;
}
