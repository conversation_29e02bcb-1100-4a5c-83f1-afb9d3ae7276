{
  "server": {
    "id": 1,
    "host": "************",
    "queryPort": 27165,
    "rconPort": 21114,
    "rconPassword": "Snow.2020",
    "logReaderMode": "tail",
    "logDir": "/home/<USER>/shared-logs",
    "ftp": {
      "host": "xxx.xxx.xxx.xxx",
      "port": 21,
      "user": "FTP Username",
      "password": "FTP Password"
    },
    "sftp": {
      "host": "xxx.xxx.xxx.xxx",
      "port": 22,
      "username": "SFTP Username",
      "password": "SFTP Password"
    },
    "adminLists": [
      {
        "type": "",
        "source": ""
      }
    ]
  },

  "connectors": {
    "discord": "MTQxMjk1Mjk4NzA1OTYxNzgzMg.GuJYhb.vGA2SJY_UbF7XMczJb4Hs9h0AlZdZ8-I5ZlNGA",
    "awnAPI": {
      "orgID": "YourOrgID",
      "creds": {
        "username": "AwnUsername",
        "password": "AwnPassword"
      }
    },
    "mysql": {
      "dialect": "mysql",
      "host": "***********",
      "port": 3306,
      "database": "s5755_squadstatus",
      "username": "u5755_PUTiy04lII",
      "password": "Ui8VtvCV!Av+rnJuumR9XJlx",
      "logging": false
    },
    "sqlite": "sqlite:database.sqlite"
  },

  "plugins": [
    {
      "plugin": "AutoKickUnassigned",
      "enabled": false,
      "warningMessage": "Join a squad, you are unassigned and will be kicked",
      "kickMessage": "Unassigned - automatically removed",
      "frequencyOfWarnings": 30,
      "unassignedTimer": 360,
      "playerThreshold": 93,
      "roundStartDelay": 900,
      "ignoreAdmins": false,
      "ignoreWhitelist": false
    },
    {
      "plugin": "SquadStats",
      "enabled": true,
      "dashboardApiUrl": "https://0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev",
      "serverId": 1,
      "enableDebugLogging": false,
      "retryAttempts": 3
    },
    {
      "plugin": "SquadStats",
      "enabled": false,
      "database": "mysql",
      "discordClient": "discord",
      "discordGuildId": "1407721428358533132",
      "publicWeb": { "enabled": true, "port": 3080 },
      "adminRoleIds": [1270807755858640946, 1270807755803852912],
      "trackTeamkills": true
    },

    {
      "plugin": "AntiCheat",
      "enabled": false,
      "debug": true,
      "discordClient": "discord",
      "channelID": "1407721428358533132",
      "BM_APIKey": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ0b2tlbiI6Ijk5M2JmZDUyMTY3ZDVjMDkiLCJpYXQiOjE3NTY1NjQ0NTgsIm5iZiI6MTc1NjU2NDQ1OCwiaXNzIjoiaHR0cHM6Ly93d3cuYmF0dGxlbWV0cmljcy5jb20iLCJzdWIiOiJ1cm46dXNlcjo3MzYzNDEifQ.J7LdndW96iCglTzFUYGnl-qTT-p9otWTd2K4ibGSSnI",
      "BM_orgID": "115707",
      "BM_banListID": "61efd380-5e05-11f0-9a61-9df735545faf",
      "banReason": "Automatic Ban - Cheating",
      "pingGroups": ["1270807755858640946", "1270807755803852912"],
      "authorizedDiscordRoles": ["982829444135809044"],
      "mapChangeOnPotentialLoadingBug": true,
      "enableInGameWarnings": true,
      "enableAutomaticBanning": true,
      "timeWindow": 120000,
      "explosions": {
        "suspected": 23,
        "detected": 50,
        "weaponModifiers": [
          { "weapon": "BP_Grenade_Frag", "multiplier": 1.0 },
          { "weapon": "BP_Deployable_C4", "multiplier": 2.0 },
          { "weapon": "BP_Grenade_RGO", "multiplier": 1.0 },
          { "weapon": "BP_Grenade_RKG3", "multiplier": 1.5 },
          { "weapon": "BP_Projectile_40mmHE", "multiplier": 0.8 },
          { "weapon": "BP_Projectile_30mmHE", "multiplier": 0.6 }
        ]
      },
      "serverMoveTimeStampExpired": {
        "suspected": 3,
        "detected": 2000,
        "delta": 100
      },
      "serverMoveTimeStampExpiredNegativeDelta": {
        "suspected": 3,
        "detected": 100
      },
      "fobHits": {
        "suspected": 999999,
        "detected": 999999
      },
      "kills": {
        "suspected": 999999,
        "detected": 999999
      },
      "wounds": {
        "suspected": 999999,
        "detected": 999999
      },
      "combinedWoundsKills": {
        "suspected": 2,
        "detected": 30
      },
      "knifeWounds": {
        "suspected": 1,
        "detected": 15
      },
      "clientNetSpeed": {
        "suspected": 18000,
        "detected": 999999
      },
      "tooManyPacketsToAck": {
        "suspected": 5,
        "detected": 999999
      },
      "lagSwitch": {
        "suspected": 2,
        "detected": 999999,
        "events": [
          "DamageCaused",
          "AttemptedToSpawnItemOutOfLoS"
        ],
        "serverMoveHistoryLength": 10
      }
    },
    {
      "plugin": "SquadWebsiteConnector",
      "enabled": false,
      "websiteUrl": "https://a6a109eb-4053-4c62-a551-01b114166fc3-00-9mly3ipy5fnd.janeway.replit.dev/",
      "apiKey": "optional-api-key-for-security",
      "updateInterval": 30000,
      "enableWebSocket": true,
      "enableHTTP": true
    },
    
    {
      "plugin": "ChatCommands",
      "enabled": false,
      "commands": [
        {
          "command": "squadjs",
          "type": "warn",
          "response": "This server is powered by SquadJS.",
          "ignoreChats": []
        }
      ]
    },

    { "plugin": "DBLog", "enabled": false, "database": "mysql", "overrideServerID": null },

    { "plugin": "DiscordAdminBroadcast", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867 },
    { "plugin": "DiscordAdminCamLogs", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867 },

    {
      "plugin": "DiscordAdminRequest",
      "enabled": false,
      "discordClient": "discord",
      "channelID": "",
      "ignoreChats": [],
      "ignorePhrases": [],
      "command": "admin",
      "pingGroups": [],
      "pingHere": false,
      "pingDelay": 60000,
      "color": 16761867,
      "warnInGameAdmins": false,
      "showInGameAdmins": true
    },
	{
      "plugin": "Anticheat",
      "enabled": true,
      "logFilePath": "/home/<USER>/shared-logs/SquadGame.log",
      "discordClient": "discord",
      "channelID": "1414071248161603594",
      "pingGroups": ["1270807755858640946", "1402326058157609030"],
      "enableFullLog": true,
      "enableEmbed": true,
      "color": 16711680,
      "explosionThreshold": 5,
      "serverMoveTimeStampExpiredThreshold": 50,
      "knifeWoundsThreshold": 3,
      "fobHitsThreshold": 10,
      "combinedWoundsKillsThreshold": 25,
      "lagSwitchThreshold": 2,
      "liveThreshold": 2,
      "seedingMinThreshold": 1,
      "interval": 30000,
      "warnInGameAdmins": false,
      "logRetentionDays": 7,
      "enableGraphGeneration": true,
      "graphCommands": ["!server-stats", "!player-graph"]
    },
    {
      "plugin": "DiscordChat",
      "enabled": false,
      "discordClient": "discord",
      "channelID": "",
      "chatColors": {},
      "color": 16761867,
      "ignoreChats": ["ChatSquad"]
    },

    { "plugin": "DiscordDebug", "enabled": false, "discordClient": "discord", "channelID": "", "events": [] },
    { "plugin": "DiscordFOBHABExplosionDamage", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867 },

    { "plugin": "DiscordKillFeed", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867, "disableCBL": false },

    { "plugin": "DiscordPlaceholder", "enabled": false, "discordClient": "discord", "command": "!placeholder", "channelID": "" },

    { "plugin": "DiscordRcon", "enabled": false, "discordClient": "discord", "channelID": "", "permissions": {}, "prependAdminNameInBroadcast": false },

    { "plugin": "DiscordRoundWinner", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867 },
    { "plugin": "DiscordRoundEnded", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867 },

    {
      "plugin": "DiscordServerStatus",
      "enabled": true,
      "discordClient": "discord",
      "messageStore": "sqlite",
      "command": "!status-server",
      "disableSubscriptions": false,
      "updateInterval": 60000,
      "setBotStatus": true
    },

    { "plugin": "DiscordSquadCreated", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867, "useEmbed": true },
    { "plugin": "DiscordSubsystemRestarter", "enabled": false, "discordClient": "discord", "role": "" },
    { "plugin": "DiscordTeamkill", "enabled": false, "discordClient": "discord", "channelID": "", "color": 16761867, "disableCBL": false },

    { "plugin": "FogOfWar", "enabled": false, "mode": 1, "delay": 10000 },

    { "plugin": "IntervalledBroadcasts", "enabled": false, "broadcasts": [], "interval": 300000 },

    { "plugin": "SeedingMode", "enabled": false, "interval": 150000, "seedingThreshold": 50, "seedingMessage": "Seeding Rules Active! Fight only over the middle flags! No FOB Hunting!", "liveEnabled": true, "liveThreshold": 52, "liveMessage": "Live!", "waitOnNewGames": true, "waitTimeOnNewGame": 30 },

    { "plugin": "SocketIOAPI", "enabled": false, "websocketPort": "", "securityToken": "" },

    { "plugin": "TeamRandomizer", "enabled": false, "command": "randomize" },

    {
      "plugin": "SquadAntiCheat",
      "enabled": false,
      "discordClient": "discord",
      "channelID": "1407721428358533132",
      "options": {
        "channelID": "1407721428358533132",
        "debug": false,
        "logFileName": "SquadGame.log",
        "disableAutomaticBan": true,
        "silentMonitoring": true,
        "reportingInterval": 300000,
        "onlyReportSignificant": true,
        "maxProcessingRate": 500,
        "batchSize": 25,
        "memoryCleanupInterval": 300000,
        "maxPlayerHistory": 50,
        "detectionConfig": {
          "Explosions": { "suspected": 30, "detected": 65, "weaponModifiers": [ { "weapon": "BP_Grenade_Frag", "multiplier": 1.0 }, { "weapon": "BP_Grenade_Smoke", "multiplier": 0.5 } ] },
          "ServerMoveTimeStampExpired": { "suspected": 150, "detected": 3000, "delta": 150 },
          "ServerMoveTimeStampExpiredNegativeDelta": { "suspected": 30, "detected": 150 },
          "FobHits": { "suspected": 20, "detected": 65 },
          "Kills": { "suspected": 30, "detected": 65 },
          "Wounds": { "suspected": 40, "detected": 80 },
          "CombinedWoundsKills": { "suspected": 30, "detected": 40 },
          "KnifeWounds": { "suspected": 8, "detected": 20 },
          "ClientNetSpeed": { "suspected": 20000, "detected": 999999 },
          "TooManyPacketsToAck": { "suspected": 8, "detected": 999999 },
          "LagSwitch": { "suspected": 3, "detected": 999999, "events": ["DamageCaused"], "serverMoveHistoryLength": 5 }
        },
        "analysisInterval": 600000,
        "preAnalysisLogLines": 5000,
        "logRetentionHours": 12,
        "autoKick": false,
        "autoKickThreshold": 200,
        "autoBan": false,
        "autoBanThreshold": 300,
        "banReason": "Anti-Cheat: Suspicious Activity Detected",
        "alertAdmins": true,
        "discordAlerts": true,
        "alertThreshold": 75,
        "alertOptionOverrides": {},
        "embedColor": 16711680,
        "pingRoles": [],
        "authorizedDiscordRoles": [],
        "mapChangeOnPotentialLoadingBug": false,
        "BM_APIKey": null,
        "BM_orgID": null,
        "BM_banListID": null
      }
    }
  ],

  "logger": {
    "verboseness": {
      "SquadServer": 1,
      "LogParser": 1,
      "RCON": 1,
      "AntiCheat": 1
    },
    "colors": {
      "SquadServer": "yellowBright",
      "SquadServerFactory": "yellowBright",
      "LogParser": "blueBright",
      "RCON": "redBright",
      "AntiCheat": "magentaBright"
    }
  }
}  [Pterodactyl Daemon]: Checking server disk space usage, this could take a few seconds...
[Pterodactyl Daemon]: Updating process configuration files...
[Pterodactyl Daemon]: Ensuring file permissions are set correctly, this could take a few seconds...
container@nookure~  Server marked as starting...
[Pterodactyl Daemon]: Pulling Docker container image, this could take a few minutes to complete...
Pulling from parkervcp/yolks 
Digest: sha256:7f44f23b93a9661a684b21bb9d3b8b5f080534e64f2379b6da48d36753b0aa2d 
Status: Image is up to date for ghcr.io/parkervcp/yolks:nodejs_20 
[Pterodactyl Daemon]: Finished pulling Docker container image
v20.19.5
:/home/<USER>
node:internal/modules/esm/resolve:283
    throw new ERR_MODULE_NOT_FOUND(
          ^
Error [ERR_MODULE_NOT_FOUND]: Cannot find module '/home/<USER>/squad-server/plugins/index.js' imported from /home/<USER>/squad-server/factory.js
    at finalizeResolution (node:internal/modules/esm/resolve:283:11)
    at moduleResolve (node:internal/modules/esm/resolve:952:10)
    at defaultResolve (node:internal/modules/esm/resolve:1188:11)
    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:708:12)
    at #cachedDefaultResolve (node:internal/modules/esm/loader:657:25)
    at ModuleLoader.resolve (node:internal/modules/esm/loader:640:38)
    at ModuleLoader.getModuleJobForImport (node:internal/modules/esm/loader:264:38)
    at ModuleJob._link (node:internal/modules/esm/module_job:168:49) {
  code: 'ERR_MODULE_NOT_FOUND',
  url: 'file:///home/<USER>/squad-server/plugins/index.js'
}
Node.js v20.19.5
container@nookure~  Server marked as offline...
[Pterodactyl Daemon]: ---------- Detected server process in a crashed state! ----------
[Pterodactyl Daemon]: Exit code: 1
[Pterodactyl Daemon]: Out of memory: false 