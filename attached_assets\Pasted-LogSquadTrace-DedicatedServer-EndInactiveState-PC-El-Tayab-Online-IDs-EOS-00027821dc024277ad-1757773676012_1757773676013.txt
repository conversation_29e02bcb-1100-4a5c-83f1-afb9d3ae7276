LogSquadTrace: [DedicatedServer]EndInactiveState(): PC=El_Tayab (Online IDs: EOS: 00027821dc024277ad9b56480a0cb66e steam: 76561199830311816)
[2025.09.12-20.49.02:726][575]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00020285fa1d40d49a497e42e17045a7
[2025.09.12-20.49.02:726][575]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00029614b1364f6487d868a11e64de85
[2025.09.12-20.49.02:726][575]LogBazaarSubsystem: [OnGetInventorySuccess] GetInventoryItems Success. Got []
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=kdawoud496 Spawn=Team2TempSpawnGroupPoliceStation DeployRole=GFI_LAT_01
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]FindPlayerStart_Implementation(): On Server PC=kdawoud496 Spawn=Team2TempSpawnGroupPoliceStation DeployRole=GFI_LAT_01 IncomingName=
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server PC=kdawoud496 Spawn=Team2TempSpawnGroupPoliceStation DeployRole=GFI_LAT_01
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server GameStartTeamID=2 PSTeamID=2 CanSpawn()=1
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=kdawoud496
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=kdawoud496 ConcretePawnClassForController=BP_Soldier_GFI_Rifleman_C
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]SpawnDefaultPawnFor_Implementation(): On Server PC=kdawoud496 DeployRole=GFI_LAT_01
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=kdawoud496
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=kdawoud496 ConcretePawnClassForController=BP_Soldier_GFI_Light_AT_C
[2025.09.12-20.49.02:890][586]LogSquad: Found a valid cached spawn location at V(X=-12872.87, Y=17778.70, Z=-13397.03) for Team2TempSpawnpoint3
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=kdawoud496
[2025.09.12-20.49.02:890][586]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=kdawoud496 ConcretePawnClassForController=BP_Soldier_GFI_Light_AT_C
[2025.09.12-20.49.02:891][586]LogSquadTrace: [DedicatedServer]OnPossess(): PC=kdawoud496 (Online IDs: EOS: 00021d231796430284b9b043b502cd3b steam: 76561199484914604) Pawn=BP_Soldier_GFI_Light_AT_C_2147463375 FullPath=BP_Soldier_GFI_Light_AT_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_Soldier_GFI_Light_AT_C_2147463375
[2025.09.12-20.49.02:893][586]LogSquadTrace: [DedicatedServer]ChangeState(): PC=kdawoud496 (Online IDs: EOS: 00021d231796430284b9b043b502cd3b steam: 76561199484914604) OldState=Inactive NewState=Playing
[2025.09.12-20.49.02:893][586]LogSquadTrace: [DedicatedServer]EndInactiveState(): PC=kdawoud496 (Online IDs: EOS: 00021d231796430284b9b043b502cd3b steam: 76561199484914604)
[2025.09.12-20.49.03:266][610]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2147482119 accept
[2025.09.12-20.49.03:266][610]LogNet: NotifyAcceptingConnection accepted from: *************:64596
[2025.09.12-20.49.03:518][626]LogNet: Join request: /Game/Maps/Logar_Valley/LogarValley_AAS_v1?Name=[DERI] M0H?SplitscreenCount=1
[2025.09.12-20.49.03:518][626]LogSquad: Login: NewPlayer: RedpointEOSIpNetConnection /Engine/Transient.RedpointEOSIpNetConnection_2147472580
[2025.09.12-20.49.03:518][626]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.03:518][626]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.03:518][626]LogGameMode: SQGameMode Login call, UniqueId:000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.03:518][626]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.03:518][626]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.03:518][626]LogSquad: PostLogin: NewPlayer: BP_PlayerController_AdminTools_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_PlayerController_AdminTools_C_2147463012 (IP: ************ | Online IDs: EOS: 000215fd35a34b5282b3964fc8a1c7fb steam: 76561199382083287)
[2025.09.12-20.49.03:518][626]LogGameMode: New player initialized
[2025.09.12-20.49.03:518][626]LogGameMode: Initialized player [DERI] M0H with 31
[2025.09.12-20.49.03:518][626]LogSquad: Player  [DERI] M0H has been added to Team 2
[2025.09.12-20.49.03:518][626]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=[DERI] M0H Spawn=nullptr DeployRole=GFI_Rifleman_01
[2025.09.12-20.49.03:518][626]LogNet: Join succeeded: [DERI] M0H
[2025.09.12-20.49.03:580][630]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2147482119 accept
[2025.09.12-20.49.03:580][630]LogNet: NotifyAcceptingConnection accepted from: *************:64596
[2025.09.12-20.49.03:580][630]LogNet: Server accepting post-challenge connection from: *************:64596
[2025.09.12-20.49.03:580][630]LogNet: RedpointEOSIpNetConnection_2147462964 setting maximum channels to: 32767
[2025.09.12-20.49.03:580][630]PacketHandlerLog: Loaded PacketHandler component: OnlineSubsystemSteam.SteamAuthComponentModuleInterface ()
[2025.09.12-20.49.03:580][630]PacketHandlerLog: Loaded PacketHandler component: AESGCMHandlerComponent ()
[2025.09.12-20.49.03:580][630]PacketHandlerLog: Loaded PacketHandler component: Engine.EngineHandlerComponentFactory (StatelessConnectHandlerComponent)
[2025.09.12-20.49.03:581][630]LogNet: NotifyAcceptedConnection: Name: OnlineBeaconHost_2147482119, TimeStamp: 09/12/25 20:49:03, [UNetConnection] RemoteAddr: *************:64596, Name: RedpointEOSIpNetConnection_2147462964, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.03:581][630]LogNet: AddClientConnection: Added client connection: [UNetConnection] RemoteAddr: *************:64596, Name: RedpointEOSIpNetConnection_2147462964, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.03:680][636]LogRedpointEOS: Warning: [LogEOSAuth] Unable to get Epic account id from product user id - No logged in user found
[2025.09.12-20.49.03:680][636]LogRedpointEOS: Verbose: [LogEOSPresence] FPresenceFeatureTypes::FUpdateSessionMessageType - GetAsEpicAccount Fail - EOS_EpicAccountId is invalid
[2025.09.12-20.49.03:721][639]LogOnline: STEAM: AUTH HANDLER: Sending auth result to user ***************** with flag success? 1
[2025.09.12-20.49.03:800][644]LogNet: Join request: /Game/Maps/Logar_Valley/LogarValley_AAS_v1?Name=AbouShoush?SplitscreenCount=1
[2025.09.12-20.49.03:800][644]LogSquad: Login: NewPlayer: RedpointEOSIpNetConnection /Engine/Transient.RedpointEOSIpNetConnection_2147472403
[2025.09.12-20.49.03:800][644]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002fd56e4884fdaafd5e2b88e2ffbfc
[2025.09.12-20.49.03:800][644]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002fd56e4884fdaafd5e2b88e2ffbfc
[2025.09.12-20.49.03:800][644]LogGameMode: SQGameMode Login call, UniqueId:0002fd56e4884fdaafd5e2b88e2ffbfc
[2025.09.12-20.49.03:800][644]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002fd56e4884fdaafd5e2b88e2ffbfc
[2025.09.12-20.49.03:800][644]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002fd56e4884fdaafd5e2b88e2ffbfc
[2025.09.12-20.49.03:800][644]LogSquad: PostLogin: NewPlayer: BP_PlayerController_AdminTools_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_PlayerController_AdminTools_C_2147462663 (IP: ************* | Online IDs: EOS: 0002fd56e4884fdaafd5e2b88e2ffbfc steam: 76561198438696918)
[2025.09.12-20.49.03:800][644]LogGameMode: New player initialized
[2025.09.12-20.49.03:800][644]LogGameMode: Initialized player AbouShoush with 32
[2025.09.12-20.49.03:800][644]LogSquad: Player  AbouShoush has been added to Team 1
[2025.09.12-20.49.03:800][644]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=AbouShoush Spawn=nullptr DeployRole=IDF_Rifleman_01
[2025.09.12-20.49.03:801][644]LogNet: Join succeeded: AbouShoush
[2025.09.12-20.49.03:946][653]LogRedpointEOS: Warning: [LogEOSAuth] Unable to get Epic account id from product user id - No logged in user found
[2025.09.12-20.49.03:946][653]LogRedpointEOS: Verbose: [LogEOSPresence] FPresenceFeatureTypes::FUpdateSessionMessageType - GetAsEpicAccount Fail - EOS_EpicAccountId is invalid
[2025.09.12-20.49.03:956][654]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2147482119 accept
[2025.09.12-20.49.03:956][654]LogNet: NotifyAcceptingConnection accepted from: *************:63778
[2025.09.12-20.49.04:003][657]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2147482119 accept
[2025.09.12-20.49.04:003][657]LogNet: NotifyAcceptingConnection accepted from: *************:63778
[2025.09.12-20.49.04:003][657]LogNet: Server accepting post-challenge connection from: *************:63778
[2025.09.12-20.49.04:003][657]LogNet: RedpointEOSIpNetConnection_2147462404 setting maximum channels to: 32767
[2025.09.12-20.49.04:004][657]PacketHandlerLog: Loaded PacketHandler component: OnlineSubsystemSteam.SteamAuthComponentModuleInterface ()
[2025.09.12-20.49.04:004][657]PacketHandlerLog: Loaded PacketHandler component: AESGCMHandlerComponent ()
[2025.09.12-20.49.04:004][657]PacketHandlerLog: Loaded PacketHandler component: Engine.EngineHandlerComponentFactory (StatelessConnectHandlerComponent)
[2025.09.12-20.49.04:004][657]LogNet: NotifyAcceptedConnection: Name: OnlineBeaconHost_2147482119, TimeStamp: 09/12/25 20:49:04, [UNetConnection] RemoteAddr: *************:63778, Name: RedpointEOSIpNetConnection_2147462404, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.04:004][657]LogNet: AddClientConnection: Added client connection: [UNetConnection] RemoteAddr: *************:63778, Name: RedpointEOSIpNetConnection_2147462404, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.04:050][660]LogOnline: STEAM: AUTH HANDLER: Sending auth result to user ***************** with flag success? 1
[2025.09.12-20.49.04:097][663]LogNet: NotifyAcceptingChannel Control 0 server OnlineBeaconHost /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.OnlineBeaconHost_2147482119: Accepted
[2025.09.12-20.49.04:097][663]LogNet: Remote platform little endian=1
[2025.09.12-20.49.04:097][663]LogNet: This platform little endian=1
[2025.09.12-20.49.04:097][663]LogRedpointEOSNetworkAuth: Verbose: *************: connection: AutomaticEncryption: Starting...
[2025.09.12-20.49.04:097][663]LogRedpointEOSNetworkAuth: Warning: The dedicated server had no public/private signing keypair set, so the connection will not be automatically encrypted.
[2025.09.12-20.49.04:097][663]LogRedpointEOSNetworkAuth: Verbose: *************: connection: AutomaticEncryption: Finished successfully.
[2025.09.12-20.49.04:097][663]LogRedpointEOSNetworkAuth: Verbose: *************: connection: All phases finished successfully.
[2025.09.12-20.49.04:097][663]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462404]: Beacon Hello
[2025.09.12-20.49.04:129][665]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462404]: Client netspeed is 18000
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Verbose: 000216f4be504c8f8df3674a2d4659da: verification: IdTokenAuth: Starting...
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 000216f4be504c8f8df3674a2d4659da because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Verbose: 000216f4be504c8f8df3674a2d4659da: verification: IdTokenAuth: Finished successfully.
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Verbose: 000216f4be504c8f8df3674a2d4659da: verification: All phases finished successfully.
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Verbose: 000216f4be504c8f8df3674a2d4659da/SQJoinBeaconClient: beacon: Immediately finishing as there are no phases to run.
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Verbose: 000216f4be504c8f8df3674a2d4659da/SQJoinBeaconClient: beacon: Finished successfully.
[2025.09.12-20.49.04:129][665]LogRedpointEOSNetworkAuth: Verbose: 000216f4be504c8f8df3674a2d4659da/SQJoinBeaconClient: beacon: All phases finished successfully.
[2025.09.12-20.49.04:129][665]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462404]: Beacon Join SQJoinBeaconClient RedpointEOS:000216f4be504c8f8df3674a2d4659da (unauthenticated)
[2025.09.12-20.49.04:144][666]LogNet: NotifyAcceptingChannel Control 0 server OnlineBeaconHost /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.OnlineBeaconHost_2147482119: Accepted
[2025.09.12-20.49.04:144][666]LogNet: Remote platform little endian=1
[2025.09.12-20.49.04:144][666]LogNet: This platform little endian=1
[2025.09.12-20.49.04:144][666]LogRedpointEOSNetworkAuth: Verbose: *************: connection: AutomaticEncryption: Starting...
[2025.09.12-20.49.04:144][666]LogRedpointEOSNetworkAuth: Warning: The dedicated server had no public/private signing keypair set, so the connection will not be automatically encrypted.
[2025.09.12-20.49.04:144][666]LogRedpointEOSNetworkAuth: Verbose: *************: connection: AutomaticEncryption: Finished successfully.
[2025.09.12-20.49.04:144][666]LogRedpointEOSNetworkAuth: Verbose: *************: connection: All phases finished successfully.
[2025.09.12-20.49.04:144][666]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462964]: Beacon Hello
[2025.09.12-20.49.04:160][667]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462404]: Handshake complete.
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Queuing 1 users for resolution (GetUsers)
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Scheduled pending callback for resolution of 1 users.
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 000216f4be504c8f8df3674a2d4659da: Ready? No ''
[2025.09.12-20.49.04:160][667]LogRedpointEOSUserCache: Verbose: ProcessQueue: Querying 1 external account mappings.
[2025.09.12-20.49.04:322][677]LogRedpointEOS: Verbose: [LogEOSConnect] FConnectClient::CacheExternalAccountInfo - ProductUserId: 000216f4be504c8f8df3674a2d4659da, AccountType: 1, AccountId: *****************, DisplayName: <Redacted>
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: ProcessQueue: Queried 1 external account mappings.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing successfully retrieved external account mappings.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: ProcessQueue: There are no Epic Games accounts to query.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing is complete after external mappings query, flushing pending callbacks and scheduling more processes if needed.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 000216f4be504c8f8df3674a2d4659da: Ready? Yes ''
[2025.09.12-20.49.04:322][677]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): All entries resolved or errored, firing callback.
[2025.09.12-20.49.04:322][677]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:322][677]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:322][677]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:322][677]SQLogJoinBeaconHost: client 000216f4be504c8f8df3674a2d4659da added to public queue. New Size: 1
[2025.09.12-20.49.04:458][686]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462964]: Client netspeed is 18000
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Verbose: 00027aa86afc46caa0db49404d35ca3a: verification: IdTokenAuth: Starting...
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 00027aa86afc46caa0db49404d35ca3a because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Verbose: 00027aa86afc46caa0db49404d35ca3a: verification: IdTokenAuth: Finished successfully.
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Verbose: 00027aa86afc46caa0db49404d35ca3a: verification: All phases finished successfully.
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Verbose: 00027aa86afc46caa0db49404d35ca3a/SQJoinBeaconClient: beacon: Immediately finishing as there are no phases to run.
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Verbose: 00027aa86afc46caa0db49404d35ca3a/SQJoinBeaconClient: beacon: Finished successfully.
[2025.09.12-20.49.04:458][686]LogRedpointEOSNetworkAuth: Verbose: 00027aa86afc46caa0db49404d35ca3a/SQJoinBeaconClient: beacon: All phases finished successfully.
[2025.09.12-20.49.04:458][686]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462964]: Beacon Join SQJoinBeaconClient RedpointEOS:00027aa86afc46caa0db49404d35ca3a (unauthenticated)
[2025.09.12-20.49.04:554][692]SQLogJoinBeaconHost: RequestClientJoin: 000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:554][692]SQLogJoinBeaconHost: Removing from PublicQueue: 000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:568][693]LogNet: NotifyAcceptingConnection accepted from: ************:57835
[2025.09.12-20.49.04:583][694]LogNet: UChannel::ReceivedSequencedBunch: Bunch.bClose == true. ChIndex == 0. Calling ConditionalCleanUp.
[2025.09.12-20.49.04:583][694]LogNet: UChannel::CleanUp: ChIndex == 0. Closing connection. [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: *************:63778, Name: RedpointEOSIpNetConnection_2147462404, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462390, UniqueId: RedpointEOS:000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:583][694]LogNet: UNetConnection::Close: [UNetConnection] RemoteAddr: *************:63778, Name: RedpointEOSIpNetConnection_2147462404, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462390, UniqueId: RedpointEOS:000216f4be504c8f8df3674a2d4659da, Channels: 4, Time: 2025.09.12-20.49.04
[2025.09.12-20.49.04:583][694]LogNet: UNetConnection::SendCloseReason:
[2025.09.12-20.49.04:583][694]LogNet:  - Result=ControlChannelClose, ErrorContext="ControlChannelClose"
[2025.09.12-20.49.04:583][694]LogNet: UChannel::Close: Sending CloseBunch. ChIndex == 0. Name: [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: *************:63778, Name: RedpointEOSIpNetConnection_2147462404, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462390, UniqueId: RedpointEOS:000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:599][695]LogNet: UNetDriver::RemoveClientConnection - Removed address *************:63778 from MappedClientConnections for: [UNetConnection] RemoteAddr: *************:63778, Name: RedpointEOSIpNetConnection_2147462404, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462390, UniqueId: RedpointEOS:000216f4be504c8f8df3674a2d4659da
[2025.09.12-20.49.04:646][698]LogNet: NotifyAcceptingConnection accepted from: ************:57835
[2025.09.12-20.49.04:646][698]LogNet: Server accepting post-challenge connection from: ************:57835
[2025.09.12-20.49.04:646][698]LogNet: RedpointEOSIpNetConnection_2147462272 setting maximum channels to: 32767
[2025.09.12-20.49.04:647][698]PacketHandlerLog: Loaded PacketHandler component: OnlineSubsystemSteam.SteamAuthComponentModuleInterface ()
[2025.09.12-20.49.04:647][698]PacketHandlerLog: Loaded PacketHandler component: AESGCMHandlerComponent ()
[2025.09.12-20.49.04:647][698]PacketHandlerLog: Loaded PacketHandler component: Engine.EngineHandlerComponentFactory (StatelessConnectHandlerComponent)
[2025.09.12-20.49.04:647][698]LogNet: NotifyAcceptedConnection: Name: Sumari_Seed_v1, TimeStamp: 09/12/25 20:49:04, [UNetConnection] RemoteAddr: ************:57835, Name: RedpointEOSIpNetConnection_2147462272, Driver: Name:GameNetDriver Def:GameNetDriver RedpointEOSNetDriver_2147482424, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.04:647][698]LogNet: AddClientConnection: Added client connection: [UNetConnection] RemoteAddr: ************:57835, Name: RedpointEOSIpNetConnection_2147462272, Driver: Name:GameNetDriver Def:GameNetDriver RedpointEOSNetDriver_2147482424, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.04:709][702]LogOnline: STEAM: AUTH HANDLER: Sending auth result to user 76561199104694716 with flag success? 1
[2025.09.12-20.49.04:756][705]LogNet: NotifyAcceptingChannel Control 0 server World /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1: Accepted
[2025.09.12-20.49.04:756][705]LogNet: Remote platform little endian=1
[2025.09.12-20.49.04:756][705]LogNet: This platform little endian=1
[2025.09.12-20.49.04:756][705]LogRedpointEOSNetworkAuth: Verbose: ************: connection: AutomaticEncryption: Starting...
[2025.09.12-20.49.04:756][705]LogRedpointEOSNetworkAuth: Warning: The dedicated server had no public/private signing keypair set, so the connection will not be automatically encrypted.
[2025.09.12-20.49.04:756][705]LogRedpointEOSNetworkAuth: Verbose: ************: connection: AutomaticEncryption: Finished successfully.
[2025.09.12-20.49.04:756][705]LogRedpointEOSNetworkAuth: Verbose: ************: connection: All phases finished successfully.
[2025.09.12-20.49.04:803][708]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147462964]: Handshake complete.
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Queuing 1 users for resolution (GetUsers)
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Scheduled pending callback for resolution of 1 users.
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 00027aa86afc46caa0db49404d35ca3a: Ready? No ''
[2025.09.12-20.49.04:803][708]LogRedpointEOSUserCache: Verbose: ProcessQueue: Querying 1 external account mappings.
[2025.09.12-20.49.04:803][708]LogRedpointEOSNetworkAuth: Verbose: 00028f9615d24bbab6f2b5eef5b70c28: verification: IdTokenAuth: Starting...
[2025.09.12-20.49.04:803][708]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 00028f9615d24bbab6f2b5eef5b70c28 because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.12-20.49.04:803][708]LogRedpointEOSNetworkAuth: Verbose: 00028f9615d24bbab6f2b5eef5b70c28: verification: IdTokenAuth: Finished successfully.
[2025.09.12-20.49.04:803][708]LogRedpointEOSNetworkAuth: Verbose: 00028f9615d24bbab6f2b5eef5b70c28: verification: All phases finished successfully.
[2025.09.12-20.49.04:803][708]LogRedpointEOSNetworkAuth: Verbose: 00028f9615d24bbab6f2b5eef5b70c28: login: AntiCheatProof: Starting...
[2025.09.12-20.49.04:803][708]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 00028f9615d24bbab6f2b5eef5b70c28: Requesting trusted client proof from remote peer 00028f9615d24bbab6f2b5eef5b70c28...
[2025.09.12-20.49.04:840][710]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [ClientAuthStatusChanged] ClientHandle: 0x409 Status: 2
[2025.09.12-20.49.04:840][710]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 0002c49c7f2a496ea4a402001c95faf6: Anti-Cheat verification status is now 'RemoteAuthComplete'.
[2025.09.12-20.49.04:840][710]LogRedpointEOSNetworkAuth: Verbose: 0002c49c7f2a496ea4a402001c95faf6: login: AntiCheatIntegrity: Finished successfully.
[2025.09.12-20.49.04:840][710]LogRedpointEOSNetworkAuth: Verbose: 0002c49c7f2a496ea4a402001c95faf6: login: All phases finished successfully.
[2025.09.12-20.49.04:840][710]LogNet: Login request: ?Name=rock5games userId: RedpointEOS:0002c49c7f2a496ea4a402001c95faf6 platform: RedpointEOS
[2025.09.12-20.49.04:840][710]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.04:840][710]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.04:840][710]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.04:840][710]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.04:840][710]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.04:840][710]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: NotifyClientAuthStatusChanged(ClientHandle: 1033, ClientAuthStatus: 2): Propagated event to OnPlayerAuthStatusChanged() handler.
[2025.09.12-20.49.04:866][712]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 00028f9615d24bbab6f2b5eef5b70c28: Received proof data from client (protected).
[2025.09.12-20.49.04:866][712]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: RegisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 00028f9615d24bbab6f2b5eef5b70c28, ClientType: 0, ClientPlatform: 0): Called
[2025.09.12-20.49.04:866][712]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x7648d6baa7f0: AddPlayer(UserId: 00028f9615d24bbab6f2b5eef5b70c28)
[2025.09.12-20.49.04:866][712]LogRedpointEOSAntiCheat: Verbose: Player tracking: 00028f9615d24bbab6f2b5eef5b70c28: Requested to add player
[2025.09.12-20.49.04:866][712]LogRedpointEOSAntiCheat: Verbose: Player tracking: 00028f9615d24bbab6f2b5eef5b70c28: Added new player, handle count is now at 1
[2025.09.12-20.49.04:866][712]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: RegisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 00028f9615d24bbab6f2b5eef5b70c28, ClientType: 0, ClientPlatform: 0): Should we register the specified player? true
[2025.09.12-20.49.04:866][712]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [RegisterClient-003] ClientHandle: 0x40b ClientType: 0 ClientPlatform: 0 AccountId_DEPRECATED: ... UserId: 000...c28 IpAddress: ... Reserved01: 0
[2025.09.12-20.49.04:866][712]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: RegisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 00028f9615d24bbab6f2b5eef5b70c28, ClientType: 0, ClientPlatform: 0): Successfully registered player with Anti-Cheat interface.
[2025.09.12-20.49.04:866][712]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 00028f9615d24bbab6f2b5eef5b70c28: Registered player with Anti-Cheat. Now waiting for Anti-Cheat verification status.
[2025.09.12-20.49.04:866][712]LogRedpointEOSNetworkAuth: Verbose: 00028f9615d24bbab6f2b5eef5b70c28: login: AntiCheatProof: Finished successfully.
[2025.09.12-20.49.04:866][712]LogRedpointEOSNetworkAuth: Verbose: 00028f9615d24bbab6f2b5eef5b70c28: login: AntiCheatIntegrity: Starting...
[2025.09.12-20.49.04:928][716]LogNet: Client netspeed is 18000
[2025.09.12-20.49.04:963][718]LogRedpointEOS: Verbose: [LogEOSConnect] FConnectClient::CacheExternalAccountInfo - ProductUserId: 00027aa86afc46caa0db49404d35ca3a, AccountType: 1, AccountId: *****************, DisplayName: <Redacted>
[2025.09.12-20.49.04:963][718]LogRedpointEOSUserCache: Verbose: ProcessQueue: Queried 1 external account mappings.
[2025.09.12-20.49.04:963][718]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing successfully retrieved external account mappings.
[2025.09.12-20.49.04:963][718]LogRedpointEOSUserCache: Verbose: ProcessQueue: There are no Epic Games accounts to query.
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing is complete after external mappings query, flushing pending callbacks and scheduling more processes if needed.
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 00027aa86afc46caa0db49404d35ca3a: Ready? Yes ''
[2025.09.12-20.49.04:964][718]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): All entries resolved or errored, firing callback.
[2025.09.12-20.49.04:964][718]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.04:964][718]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.04:964][718]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.04:964][718]SQLogJoinBeaconHost: client 00027aa86afc46caa0db49404d35ca3a added to public queue. New Size: 1
[2025.09.12-20.49.05:119][728]LogSquad: USQGameState: Server Tick Rate: 63.79
[2025.09.12-20.49.05:542][755]SQLogJoinBeaconHost: RequestClientJoin: 00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.05:542][755]SQLogJoinBeaconHost: Removing from PublicQueue: 00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.05:681][764]LogNet: UChannel::ReceivedSequencedBunch: Bunch.bClose == true. ChIndex == 0. Calling ConditionalCleanUp.
[2025.09.12-20.49.05:681][764]LogNet: UChannel::CleanUp: ChIndex == 0. Closing connection. [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: *************:64596, Name: RedpointEOSIpNetConnection_2147462964, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462317, UniqueId: RedpointEOS:00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.05:681][764]LogNet: UNetConnection::Close: [UNetConnection] RemoteAddr: *************:64596, Name: RedpointEOSIpNetConnection_2147462964, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462317, UniqueId: RedpointEOS:00027aa86afc46caa0db49404d35ca3a, Channels: 4, Time: 2025.09.12-20.49.05
[2025.09.12-20.49.05:681][764]LogNet: UNetConnection::SendCloseReason:
[2025.09.12-20.49.05:681][764]LogNet:  - Result=ControlChannelClose, ErrorContext="ControlChannelClose"
[2025.09.12-20.49.05:681][764]LogNet: UChannel::Close: Sending CloseBunch. ChIndex == 0. Name: [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: *************:64596, Name: RedpointEOSIpNetConnection_2147462964, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462317, UniqueId: RedpointEOS:00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.05:696][765]LogNet: UNetDriver::RemoveClientConnection - Removed address *************:64596 from MappedClientConnections for: [UNetConnection] RemoteAddr: *************:64596, Name: RedpointEOSIpNetConnection_2147462964, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147462317, UniqueId: RedpointEOS:00027aa86afc46caa0db49404d35ca3a
[2025.09.12-20.49.05:858][775]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [ClientAuthStatusChanged] ClientHandle: 0x40b Status: 1
[2025.09.12-20.49.05:858][775]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 00028f9615d24bbab6f2b5eef5b70c28: Anti-Cheat verification status is now 'LocalAuthComplete'.
[2025.09.12-20.49.05:858][775]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: NotifyClientAuthStatusChanged(ClientHandle: 1035, ClientAuthStatus: 1): Propagated event to OnPlayerAuthStatusChanged() handler.
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=FLAVA SAVA Spawn=Team1TempSpawnGroupAncientQanat DeployRole=IDF_Grenadier_01
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]FindPlayerStart_Implementation(): On Server PC=FLAVA SAVA Spawn=Team1TempSpawnGroupAncientQanat DeployRole=IDF_Grenadier_01 IncomingName=
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server PC=FLAVA SAVA Spawn=Team1TempSpawnGroupAncientQanat DeployRole=IDF_Grenadier_01
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server GameStartTeamID=1 PSTeamID=1 CanSpawn()=1
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=FLAVA SAVA
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=FLAVA SAVA ConcretePawnClassForController=BP_Soldier_IDF_Army_Backpack_Sideways_NoMitznefet_C
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]SpawnDefaultPawnFor_Implementation(): On Server PC=FLAVA SAVA DeployRole=IDF_Grenadier_01
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=FLAVA SAVA
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=FLAVA SAVA ConcretePawnClassForController=BP_Soldier_IDF_Army_MG_Gunner_C
[2025.09.12-20.49.07:029][850]LogSquad: Found a valid cached spawn location at V(X=8677.03, Y=12344.96, Z=-13398.53) for Team1TempSpawnpoint2
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=FLAVA SAVA
[2025.09.12-20.49.07:029][850]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=FLAVA SAVA ConcretePawnClassForController=BP_Soldier_IDF_Army_MG_Gunner_C
[2025.09.12-20.49.07:030][850]LogSquadTrace: [DedicatedServer]OnPossess(): PC=FLAVA SAVA (Online IDs: EOS: 0002b148670943b3a286469a6db75634 steam: 76561199716945747) Pawn=BP_Soldier_IDF_Army_MG_Gunner_C_2147462067 FullPath=BP_Soldier_IDF_Army_MG_Gunner_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_Soldier_IDF_Army_MG_Gunner_C_2147462067
[2025.09.12-20.49.07:031][850]LogSquadTrace: [DedicatedServer]ChangeState(): PC=FLAVA SAVA (Online IDs: EOS: 0002b148670943b3a286469a6db75634 steam: 76561199716945747) OldState=Inactive NewState=Playing
[2025.09.12-20.49.07:031][850]LogSquadTrace: [DedicatedServer]EndInactiveState(): PC=FLAVA SAVA (Online IDs: EOS: 0002b148670943b3a286469a6db75634 steam: 76561199716945747)
[2025.09.12-20.49.07:234][863]LogSquad: JANO (Online IDs: EOS: 00024b5887c649bc8b2072488629717b steam: 76561198853347085) has created Squad 2 (Squad Name: Squad 2) on Israel Defense Force
[2025.09.12-20.49.07:499][880]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2147482119 accept
[2025.09.12-20.49.07:499][880]LogNet: NotifyAcceptingConnection accepted from: *************:64456
[2025.09.12-20.49.07:515][881]LogNet: Join request: /Game/Maps/Logar_Valley/LogarValley_AAS_v1?Name=rock5games?SplitscreenCount=1
[2025.09.12-20.49.07:515][881]LogSquad: Login: NewPlayer: RedpointEOSIpNetConnection /Engine/Transient.RedpointEOSIpNetConnection_2147470362
[2025.09.12-20.49.07:515][881]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.07:515][881]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.07:515][881]LogGameMode: SQGameMode Login call, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.07:516][881]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.07:516][881]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002c49c7f2a496ea4a402001c95faf6
[2025.09.12-20.49.07:516][881]LogSquad: PostLogin: NewPlayer: BP_PlayerController_AdminTools_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_PlayerController_AdminTools_C_2147461969 (IP: *************** | Online IDs: EOS: 0002c49c7f2a496ea4a402001c95faf6 steam: 76561198993777319)
[2025.09.12-20.49.07:516][881]LogGameMode: New player initialized
[2025.09.12-20.49.07:516][881]LogGameMode: Initialized player rock5games with 33
[2025.09.12-20.49.07:516][881]LogSquad: Player  rock5games has been added to Team 2
[2025.09.12-20.49.07:516][881]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=rock5games Spawn=nullptr DeployRole=GFI_Rifleman_01
[2025.09.12-20.49.07:516][881]LogNet: Join succeeded: rock5games
[2025.09.12-20.49.07:594][886]LogBazaarSubsystem: [GetInventoryItems] GetInventoryItems Started for PlayerID: 000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.07:672][891]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2147482119 accept
[2025.09.12-20.49.07:672][891]LogNet: NotifyAcceptingConnection accepted from: *************:64456
[2025.09.12-20.49.07:672][891]LogNet: Server accepting post-challenge connection from: *************:64456
[2025.09.12-20.49.07:672][891]LogNet: RedpointEOSIpNetConnection_2147461847 setting maximum channels to: 32767
[2025.09.12-20.49.07:672][891]PacketHandlerLog: Loaded PacketHandler component: OnlineSubsystemSteam.SteamAuthComponentModuleInterface ()
[2025.09.12-20.49.07:672][891]PacketHandlerLog: Loaded PacketHandler component: AESGCMHandlerComponent ()
[2025.09.12-20.49.07:672][891]PacketHandlerLog: Loaded PacketHandler component: Engine.EngineHandlerComponentFactory (StatelessConnectHandlerComponent)
[2025.09.12-20.49.07:672][891]LogNet: NotifyAcceptedConnection: Name: OnlineBeaconHost_2147482119, TimeStamp: 09/12/25 20:49:07, [UNetConnection] RemoteAddr: *************:64456, Name: RedpointEOSIpNetConnection_2147461847, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.07:672][891]LogNet: AddClientConnection: Added client connection: [UNetConnection] RemoteAddr: *************:64456, Name: RedpointEOSIpNetConnection_2147461847, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.12-20.49.07:678][891]LogRedpointEOS: Warning: [LogEOSAuth] Unable to get Epic account id from product user id - No logged in user found
[2025.09.12-20.49.07:678][891]LogRedpointEOS: Verbose: [LogEOSPresence] FPresenceFeatureTypes::FUpdateSessionMessageType - GetAsEpicAccount Fail - EOS_EpicAccountId is invalid
[2025.09.12-20.49.07:829][901]LogOnline: STEAM: AUTH HANDLER: Sending auth result to user ***************** with flag success? 1
[2025.09.12-20.49.07:931][907]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00020285fa1d40d49a497e42e17045a7
[2025.09.12-20.49.07:931][907]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000215fd35a34b5282b3964fc8a1c7fb
[2025.09.12-20.49.07:931][907]LogBazaarSubsystem: [OnGetInventorySuccess] GetInventoryItems Success. Got []
[2025.09.12-20.49.07:985][911]LogNet: NotifyAcceptingChannel Control 0 server OnlineBeaconHost /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.OnlineBeaconHost_2147482119: Accepted
[2025.09.12-20.49.07:985][911]LogNet: Remote platform little endian=1
[2025.09.12-20.49.07:985][911]LogNet: This platform little endian=1
[2025.09.12-20.49.07:985][911]LogRedpointEOSNetworkAuth: Verbose: *************: connection: AutomaticEncryption: Starting...
[2025.09.12-20.49.07:985][911]LogRedpointEOSNetworkAuth: Warning: The dedicated server had no public/private signing keypair set, so the connection will not be automatically encrypted.
[2025.09.12-20.49.07:985][911]LogRedpointEOSNetworkAuth: Verbose: *************: connection: AutomaticEncryption: Finished successfully.
[2025.09.12-20.49.07:985][911]LogRedpointEOSNetworkAuth: Verbose: *************: connection: All phases finished successfully.
[2025.09.12-20.49.07:985][911]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147461847]: Beacon Hello
[2025.09.12-20.49.08:127][920]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147461847]: Client netspeed is 18000
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Verbose: 00029cacf8634c98b369a789ccd642c6: verification: IdTokenAuth: Starting...
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 00029cacf8634c98b369a789ccd642c6 because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Verbose: 00029cacf8634c98b369a789ccd642c6: verification: IdTokenAuth: Finished successfully.
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Verbose: 00029cacf8634c98b369a789ccd642c6: verification: All phases finished successfully.
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Verbose: 00029cacf8634c98b369a789ccd642c6/SQJoinBeaconClient: beacon: Immediately finishing as there are no phases to run.
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Verbose: 00029cacf8634c98b369a789ccd642c6/SQJoinBeaconClient: beacon: Finished successfully.
[2025.09.12-20.49.08:127][920]LogRedpointEOSNetworkAuth: Verbose: 00029cacf8634c98b369a789ccd642c6/SQJoinBeaconClient: beacon: All phases finished successfully.
[2025.09.12-20.49.08:127][920]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147461847]: Beacon Join SQJoinBeaconClient RedpointEOS:00029cacf8634c98b369a789ccd642c6 (unauthenticated)
[2025.09.12-20.49.08:299][931]LogBeacon: OnlineBeaconHost_2147482119[RedpointEOSIpNetConnection_2147461847]: Handshake complete.
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Queuing 1 users for resolution (GetUsers)
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Scheduled pending callback for resolution of 1 users.
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 00029cacf8634c98b369a789ccd642c6: Ready? No ''
[2025.09.12-20.49.08:299][931]LogRedpointEOSUserCache: Verbose: ProcessQueue: Querying 1 external account mappings.
[2025.09.12-20.49.08:379][936]LogScript: Warning: Script Msg: Modulo by zero: Percent_IntInt
[2025.09.12-20.49.08:472][942]LogBazaarSubsystem: [GetInventoryItems] GetInventoryItems Started for PlayerID: 000212d67fae402a826575e0dac80cb3
[2025.09.12-20.49.08:494][943]LogRedpointEOS: Verbose: [LogEOSConnect] FConnectClient::CacheExternalAccountInfo - ProductUserId: 00029cacf8634c98b369a789ccd642c6, AccountType: 1, AccountId: *****************, DisplayName: <Redacted>
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: ProcessQueue: Queried 1 external account mappings.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing successfully retrieved external account mappings.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: ProcessQueue: There are no Epic Games accounts to query.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing is complete after external mappings query, flushing pending callbacks and scheduling more processes if needed.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 00029cacf8634c98b369a789ccd642c6: Ready? Yes ''
[2025.09.12-20.49.08:494][943]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): All entries resolved or errored, firing callback.
[2025.09.12-20.49.08:494][943]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:494][943]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:494][943]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:494][943]SQLogJoinBeaconHost: client 00029cacf8634c98b369a789ccd642c6 added to public queue. New Size: 1
[2025.09.12-20.49.08:552][947]SQLogJoinBeaconHost: RequestClientJoin: 00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:552][947]SQLogJoinBeaconHost: Removing from PublicQueue: 00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:691][956]LogNet: UChannel::ReceivedSequencedBunch: Bunch.bClose == true. ChIndex == 0. Calling ConditionalCleanUp.
[2025.09.12-20.49.08:691][956]LogNet: UChannel::CleanUp: ChIndex == 0. Closing connection. [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: *************:64456, Name: RedpointEOSIpNetConnection_2147461847, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147461578, UniqueId: RedpointEOS:00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:691][956]LogNet: UNetConnection::Close: [UNetConnection] RemoteAddr: *************:64456, Name: RedpointEOSIpNetConnection_2147461847, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147461578, UniqueId: RedpointEOS:00029cacf8634c98b369a789ccd642c6, Channels: 4, Time: 2025.09.12-20.49.08
[2025.09.12-20.49.08:691][956]LogNet: UNetConnection::SendCloseReason:
[2025.09.12-20.49.08:691][956]LogNet:  - Result=ControlChannelClose, ErrorContext="ControlChannelClose"
[2025.09.12-20.49.08:691][956]LogNet: UChannel::Close: Sending CloseBunch. ChIndex == 0. Name: [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: *************:64456, Name: RedpointEOSIpNetConnection_2147461847, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147461578, UniqueId: RedpointEOS:00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:707][957]LogNet: UNetDriver::RemoveClientConnection - Removed address *************:64456 from MappedClientConnections for: [UNetConnection] RemoteAddr: *************:64456, Name: RedpointEOSIpNetConnection_2147461847, Driver: Name:RedpointEOSNetDriver_2147482118 Def:BeaconNetDriver RedpointEOSNetDriver_2147482118, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2147461578, UniqueId: RedpointEOS:00029cacf8634c98b369a789ccd642c6
[2025.09.12-20.49.08:716][957]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00020285fa1d40d49a497e42e17045a7
[2025.09.12-20.49.08:716][957]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000212d67fae402a826575e0dac80cb3
[2025.09.12-20.49.08:716][957]LogBazaarSubsystem: [OnGetInventorySuccess] GetInventoryItems Success. Got []
[2025.09.12-20.49.09:365][999]LogNet: UChannel::ReceivedSequencedBunch: Bunch.bClose == true. ChIndex == 0. Calling ConditionalCleanUp.
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: UnregisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 0002f54015b043f4937ebaf63af28f1c): Called
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: UnregisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 0002f54015b043f4937ebaf63af28f1c): Player tracking indicated we should try to deregister player with Anti-Cheat interface before removing them from tracking.
[2025.09.12-20.49.09:365][999]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [UnregisterClient-001] ClientHandle: 0x3ff
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: UnregisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 0002f54015b043f4937ebaf63af28f1c): Successfully unregistered player from Anti-Cheat interface.
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x7648d6baa7f0: RemovePlayer(UserId: 0002f54015b043f4937ebaf63af28f1c)
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Player tracking: 0002f54015b043f4937ebaf63af28f1c: Requested to remove player
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Player tracking: 0002f54015b043f4937ebaf63af28f1c: Removed player as handle count reached 0
[2025.09.12-20.49.09:365][999]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: UnregisterPlayer(Session: 14C2708E1E514A13951EF5CD55E37B89, UserId: 0002f54015b043f4937ebaf63af28f1c): Removed player from player tracking.
[2025.09.12-20.49.09:365][999]LogNet: UChannel::CleanUp: ChIndex == 0. Closing connection. [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: ***************:14902, Name: RedpointEOSIpNetConnection_2147477968, Driver: Name:GameNetDriver Def:GameNetDriver RedpointEOSNetDriver_2147482424, IsServer: YES, PC: BP_PlayerController_AdminTools_C_2147472519, Owner: BP_PlayerController_AdminTools_C_2147472519, UniqueId: RedpointEOS:0002f54015b043f4937ebaf63af28f1c
[2025.09.12-20.49.09:365][999]LogNet: UNetConnection::Close: [UNetConnection] RemoteAddr: ***************:14902, Name: RedpointEOSIpNetConnection_2147477968, Driver: Name:GameNetDriver Def:GameNetDriver RedpointEOSNetDriver_2147482424, IsServer: YES, PC: BP_PlayerController_AdminTools_C_2147472519, Owner: BP_PlayerController_AdminTools_C_2147472519, UniqueId: RedpointEOS:0002f54015b043f4937ebaf63af28f1c, Channels: 282, Time: 2025.09.12-20.49.09
[2025.09.12-20.49.09:365][999]LogNet: UNetConnection::SendCloseReason:
[2025.09.12-20.49.09:365][999]LogNet:  - Result=ControlChannelClose, ErrorContext="ControlChannelClose"
[2025.09.12-20.49.09:365][999]LogNet: UChannel::Close: Sending CloseBunch. ChIndex == 0. Name: [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: ***************:14902, Name: RedpointEOSIpNetConnection_2147477968, Driver: Name:GameNetDriver Def:GameNetDriver RedpointEOSNetDriver_2147482424, IsServer: YES, PC: BP_PlayerController_AdminTools_C_2147472519, Owner: BP_PlayerController_AdminTools_C_2147472519, UniqueId: RedpointEOS:0002f54015b043f4937ebaf63af28f1c
[2025.09.12-20.49.09:381][  0]LogNet: UNetDriver::RemoveClientConnection - Removed address ***************:14902 from MappedClientConnections for: [UNetConnection] RemoteAddr: ***************:14902, Name: RedpointEOSIpNetConnection_2147477968, Driver: Name:GameNetDriver Def:GameNetDriver RedpointEOSNetDriver_2147482424, IsServer: YES, PC: BP_PlayerController_AdminTools_C_2147472519, Owner: BP_PlayerController_AdminTools_C_2147472519, UniqueId: RedpointEOS:0002f54015b043f4937ebaf63af28f1c
[2025.09.12-20.49.09:382][  0]LogSquad: RotorWashEffectListener::EndPlay (reason 0)
[2025.09.12-20.49.09:527][  9]LogRedpointEOS: Warning: [LogEOSAuth] Unable to get Epic account id from product user id - No logged in user found
[2025.09.12-20.49.09:527][  9]LogRedpointEOS: Verbose: [LogEOSPresence] FPresenceFeatureTypes::FUpdateSessionMessageType - GetAsEpicAccount Fail - EOS_EpicAccountId is invalid
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=strider615615 Spawn=BP_ForwardBaseSpawn_C_2147481980 DeployRole=GFI_Raider_01
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]FindPlayerStart_Implementation(): On Server PC=strider615615 Spawn=BP_ForwardBaseSpawn_C_2147481980 DeployRole=GFI_Raider_01 IncomingName=
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server PC=strider615615 Spawn=BP_ForwardBaseSpawn_C_2147481980 DeployRole=GFI_Raider_01
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server GameStartTeamID=2 PSTeamID=2 CanSpawn()=1
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=strider615615
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=strider615615 ConcretePawnClassForController=BP_Soldier_GFI_Rifleman_C
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]SpawnDefaultPawnFor_Implementation(): On Server PC=strider615615 DeployRole=GFI_Raider_01
[2025.09.12-20.49.09:851][ 30]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=strider615615
[2025.09.12-20.49.09:852][ 30]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=strider615615 ConcretePawnClassForController=BP_Soldier_GFI_Engineer_C
[2025.09.12-20.49.09:852][ 30]LogSquad: Found a valid cached spawn location at V(X=-15370.00, Y=19740.00, Z=-13397.55) for BP_ForwardBaseSpawn_C_2147481980
[2025.09.12-20.49.09:852][ 30]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=strider615615
[2025.09.12-20.49.09:852][ 30]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=strider615615 ConcretePawnClassForController=BP_Soldier_GFI_Engineer_C
[2025.09.12-20.49.09:853][ 30]LogSquadTrace: [DedicatedServer]OnPossess(): PC=strider615615 (Online IDs: EOS: 0002d74e7df14b7585b57b6bb5800072 steam: 76561198978877010) Pawn=BP_Soldier_GFI_Engineer_C_2147461297 FullPath=BP_Soldier_GFI_Engineer_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_Soldier_GFI_Engineer_C_2147461297
[2025.09.12-20.49.09:853][ 30]LogSquadTrace: [DedicatedServer]ChangeState(): PC=strider615615 (Online IDs: EOS: 0002d74e7df14b7585b57b6bb5800072 steam: 76561198978877010) OldState=Inactive NewState=Playing
[2025.09.12-20.49.09:853][ 30]LogSquadTrace: [DedicatedServer]EndInactiveState(): PC=strider615615 (Online IDs: EOS: 0002d74e7df14b7585b57b6bb5800072 steam: 76561198978877010)
[2025.09.12-20.49.09:886][ 32]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [ClientAuthStatusChanged] ClientHandle: 0x40a Status: 2
[2025.09.12-20.49.09:886][ 32]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 00028a18fca34cb7a3b80418736dd417: Anti-Cheat verification status is now 'RemoteAuthComplete'.
[2025.09.12-20.49.09:886][ 32]LogRedpointEOSNetworkAuth: Verbose: 00028a18fca34cb7a3b80418736dd417: login: AntiCheatIntegrity: Finished successfully.
[2025.09.12-20.49.09:886][ 32]LogRedpointEOSNetworkAuth: Verbose: 00028a18fca34cb7a3b80418736dd417: login: All phases finished successfully.
[2025.09.12-20.49.09:886][ 32]LogNet: Login request: ?Name=RB´Ahmad userId: RedpointEOS:00028a18fca34cb7a3b80418736dd417 platform: RedpointEOS
[2025.09.12-20.49.09:886][ 32]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00028a18fca34cb7a3b80418736dd417
[2025.09.12-20.49.09:886][ 32]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00028a18fca34cb7a3b80418736dd417
[2025.09.12-20.49.09:886][ 32]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00028a18fca34cb7a3b80418736dd417
[2025.09.12-20.49.09:886][ 32]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00028a18fca34cb7a3b80418736dd417
[2025.09.12-20.49.09:886][ 32]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00028a18fca34cb7a3b80418736dd417
[2025.09.12-20.49.09:886][ 32]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x76494cfbd010: NotifyClientAuthStatusChanged(ClientHandle: 1034, ClientAuthStatus: 2): Propagated event to OnPlayerAuthStatusChanged() handler.
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=HUNTER Spawn=Team1TempSpawnGroupAncientQanat DeployRole=IDF_Medic_02
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]FindPlayerStart_Implementation(): On Server PC=HUNTER Spawn=Team1TempSpawnGroupAncientQanat DeployRole=IDF_Medic_02 IncomingName=
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server PC=HUNTER Spawn=Team1TempSpawnGroupAncientQanat DeployRole=IDF_Medic_02
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server GameStartTeamID=1 PSTeamID=1 CanSpawn()=1
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=HUNTER
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=HUNTER ConcretePawnClassForController=BP_Soldier_IDF_Army_Backpack_Sideways_NoMitznefet_C
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]SpawnDefaultPawnFor_Implementation(): On Server PC=HUNTER DeployRole=IDF_Medic_02
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=HUNTER
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=HUNTER ConcretePawnClassForController=BP_Soldier_IDF_Army_Backpack_Sideways_C
[2025.09.12-20.49.09:930][ 35]LogSquad: Found a valid cached spawn location at V(X=8275.80, Y=12353.54, Z=-13398.53) for Team1TempSpawnpoint2
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=HUNTER
[2025.09.12-20.49.09:930][ 35]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=HUNTER ConcretePawnClassForController=BP_Soldier_IDF_Army_Backpack_Sideways_C
[2025.09.12-20.49.09:931][ 35]LogSquadTrace: [DedicatedServer]OnPossess(): PC=HUNTER (Online IDs: EOS: 00028840c83d455fbfae9ce047d221cb steam: 76561198414422928) Pawn=BP_Soldier_IDF_Army_Backpack_Sideways_C_2147461264 FullPath=BP_Soldier_IDF_Army_Backpack_Sideways_C /Game/Maps/Sumari/Gameplay_Layers/Sumari_Seed_v1.Sumari_Seed_v1:PersistentLevel.BP_Soldier_IDF_Army_Backpack_Sideways_C_2147461264
[2025.09.12-20.49.09:932][ 35]LogSquadTrace: [DedicatedServer]ChangeState(): PC=HUNTER (Online IDs: EOS: 00028840c83d455fbfae9ce047d221cb steam: 76561198414422928) OldState=Inactive NewState=Playing
[2025.09.12-20.49.09:932][ 35]LogSquadTrace: [DedicatedServer]EndInactiveState(): PC=HUNTER (Online IDs: EOS: 