import Leaderboard from '../Leaderboard';

export default function LeaderboardExample() {
  const killsLeaderboard = [
    { rank: 1, name: "<PERSON><PERSON>", steamId: "76561199585954249", value: 42 },
    { rank: 2, name: "<PERSON><PERSON><PERSON><PERSON>", steamId: "76561198123456789", value: 38 },
    { rank: 3, name: "<PERSON><PERSON>", steamId: "76561198987654321", value: 35 },
    { rank: 4, name: "<PERSON>niper<PERSON><PERSON>", steamId: "76561198111222333", value: 32 },
    { rank: 5, name: "Medics<PERSON><PERSON>", steamId: "76561198444555666", value: 28 }
  ];

  const kdLeaderboard = [
    { rank: 1, name: "<PERSON>Sni<PERSON>", steamId: "76561198777888999", value: 3.45 },
    { rank: 2, name: "PrecisionKing", steamId: "76561198000111222", value: 2.89 },
    { rank: 3, name: "<PERSON><PERSON><PERSON>", steamId: "76561198333444555", value: 2.67 },
    { rank: 4, name: "<PERSON><PERSON>", steamId: "76561198666777888", value: 2.34 },
    { rank: 5, name: "SteadyHand", steamId: "76561198999000111", value: 2.12 }
  ];

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Leaderboard
        title="Top Killers"
        entries={killsLeaderboard}
        metric="kills"
        period="week"
      />
      <Leaderboard
        title="Best K/D Ratio"
        entries={kdLeaderboard}
        metric="K/D"
        period="month"
      />
    </div>
  );
}