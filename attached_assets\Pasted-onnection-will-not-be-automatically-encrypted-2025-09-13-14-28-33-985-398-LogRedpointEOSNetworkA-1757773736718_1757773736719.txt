onnection will not be automatically encrypted.
[2025.09.13-14.28.33:985][398]LogRedpointEOSNetworkAuth: Verbose: **************: connection: AutomaticEncryption: Finished successfully.
[2025.09.13-14.28.33:985][398]LogRedpointEOSNetworkAuth: Verbose: **************: connection: All phases finished successfully.
[2025.09.13-14.28.34:016][400]LogRedpointEOSNetworkAuth: Verbose: 0002688b656847fe8aaf8c687e423976: verification: IdTokenAuth: Starting...
[2025.09.13-14.28.34:016][400]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 0002688b656847fe8aaf8c687e423976 because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.13-14.28.34:016][400]LogRedpointEOSNetworkAuth: Verbose: 0002688b656847fe8aaf8c687e423976: verification: IdTokenAuth: Finished successfully.
[2025.09.13-14.28.34:016][400]LogRedpointEOSNetworkAuth: Verbose: 0002688b656847fe8aaf8c687e423976: verification: All phases finished successfully.
[2025.09.13-14.28.34:016][400]LogRedpointEOSNetworkAuth: Verbose: 0002688b656847fe8aaf8c687e423976: login: AntiCheatProof: Starting...
[2025.09.13-14.28.34:016][400]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 0002688b656847fe8aaf8c687e423976: Requesting trusted client proof from remote peer 0002688b656847fe8aaf8c687e423976...
[2025.09.13-14.28.34:077][404]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 0002688b656847fe8aaf8c687e423976: Received proof data from client (protected).
[2025.09.13-14.28.34:077][404]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x70f0d5f3e810: RegisterPlayer(Session: ********************************, UserId: 0002688b656847fe8aaf8c687e423976, ClientType: 0, ClientPlatform: 0): Called
[2025.09.13-14.28.34:077][404]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x70f06cce98f0: AddPlayer(UserId: 0002688b656847fe8aaf8c687e423976)
[2025.09.13-14.28.34:077][404]LogRedpointEOSAntiCheat: Verbose: Player tracking: 0002688b656847fe8aaf8c687e423976: Requested to add player
[2025.09.13-14.28.34:077][404]LogRedpointEOSAntiCheat: Verbose: Player tracking: 0002688b656847fe8aaf8c687e423976: Added new player, handle count is now at 1
[2025.09.13-14.28.34:077][404]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x70f0d5f3e810: RegisterPlayer(Session: ********************************, UserId: 0002688b656847fe8aaf8c687e423976, ClientType: 0, ClientPlatform: 0): Should we register the specified player? true
[2025.09.13-14.28.34:077][404]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [RegisterClient-003] ClientHandle: 0x6e7 ClientType: 0 ClientPlatform: 0 AccountId_DEPRECATED: ... UserId: 000...976 IpAddress: ... Reserved01: 0
[2025.09.13-14.28.34:077][404]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x70f0d5f3e810: RegisterPlayer(Session: ********************************, UserId: 0002688b656847fe8aaf8c687e423976, ClientType: 0, ClientPlatform: 0): Successfully registered player with Anti-Cheat interface.
[2025.09.13-14.28.34:077][404]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 0002688b656847fe8aaf8c687e423976: Registered player with Anti-Cheat. Now waiting for Anti-Cheat verification status.
[2025.09.13-14.28.34:077][404]LogRedpointEOSNetworkAuth: Verbose: 0002688b656847fe8aaf8c687e423976: login: AntiCheatProof: Finished successfully.
[2025.09.13-14.28.34:077][404]LogRedpointEOSNetworkAuth: Verbose: 0002688b656847fe8aaf8c687e423976: login: AntiCheatIntegrity: Starting...
[2025.09.13-14.28.34:125][407]LogTextFormatter: Warning: Failed to parse argument "seatnumber" as a number (using "0" as a fallback). Please check your format string for errors: "Can't enter seat {seatnumber} since it is already in use and the player can't be booted".
[2025.09.13-14.28.34:144][408]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002af484cf440c48ea2f035ccac642d
[2025.09.13-14.28.34:144][408]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:000226434f6e45afa001f59fd713ae72
[2025.09.13-14.28.34:159][409]LogNet: Join request: /Game/Maps/Logar_Valley/LogarValley_AAS_v1?Name=muzzammilfarooq?SplitscreenCount=1
[2025.09.13-14.28.34:159][409]LogSquad: Login: NewPlayer: RedpointEOSIpNetConnection /Engine/Transient.RedpointEOSIpNetConnection_2145096860
[2025.09.13-14.28.34:159][409]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.34:159][409]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.34:159][409]LogGameMode: SQGameMode Login call, UniqueId:00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.34:160][409]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.34:160][409]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.34:160][409]LogSquad: PostLogin: NewPlayer: BP_PlayerController_AdminTools_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_PlayerController_AdminTools_C_2145096077 (IP: ************* | Online IDs: EOS: 00026d06efcb4f7fa024899b2b0647ea steam: 76561199061015065)
[2025.09.13-14.28.34:160][409]LogGameMode: New player initialized
[2025.09.13-14.28.34:160][409]LogGameMode: Initialized player muzzammilfarooq with 150
[2025.09.13-14.28.34:160][409]LogSquad: Player  muzzammilfarooq has been added to Team 2
0111[2025.09.13-14.28.34:160][409]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=muzzammilfarooq Spawn=nullptr DeployRole=GFI_Rifleman_01
[2025.09.13-14.28.34:161][409]LogNet: Join succeeded: muzzammilfarooq
[2025.09.13-14.28.34:334][419]LogRedpointEOS: Warning: [LogEOSAuth] Unable to get Epic account id from product user id - No logged in user found
[2025.09.13-14.28.34:334][419]LogRedpointEOS: Verbose: [LogEOSPresence] FPresenceFeatureTypes::FUpdateSessionMessageType - GetAsEpicAccount Fail - EOS_EpicAccountId is invalid
[2025.09.13-14.28.34:384][422]LogRedpointEOS: Verbose: [LogEOSAntiCheat] [ProcessPendingMessages] Kick requested for Client: 0x651, discarding message
[2025.09.13-14.28.34:434][426]LogBlueprintUserMessages: [BP_SAT_Player] KillerVelocity:X=161.125 Y=19.776 Z=0.000
[2025.09.13-14.28.34:434][426]LogBlueprintUserMessages: [BP_SAT_Player] HitRotationValidation:0.035753
[2025.09.13-14.28.34:434][426]PIE: SATLog: Hit: Bip01_R_Thigh
[2025.09.13-14.28.34:434][426]LogSquad: Player: [DERI] M0H ActualDamage=41.875000 from  Z1PO (Online IDs: EOS: 00020eec51be45fca3025ff7c8b6cbf4 steam: ***************** | Player Controller ID: BP_PlayerController_AdminTools_C_2145312974)caused by BP_SCAR-H_Elcan_Suppressed_Black_C_2145123807
[2025.09.13-14.28.34:473][428]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:40] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 169/500
[2025.09.13-14.28.34:513][431]LogTextFormatter: Warning: Failed to parse argument "seatnumber" as a number (using "0" as a fallback). Please check your format string for errors: "Can't enter seat {seatnumber} since it is already in use and the player can't be booted".
[2025.09.13-14.28.34:562][434]LogBlueprintUserMessages: [BP_SAT_Player] KillerVelocity:X=161.265 Y=18.277 Z=0.000
[2025.09.13-14.28.34:562][434]LogBlueprintUserMessages: [BP_SAT_Player] HitRotationValidation:0.033076
[2025.09.13-14.28.34:562][434]PIE: SATLog: Hit: Bip01_R_Calf
[2025.09.13-14.28.34:562][434]LogSquad: Player: [DERI] M0H ActualDamage=33.500000 from  Z1PO (Online IDs: EOS: 00020eec51be45fca3025ff7c8b6cbf4 steam: ***************** | Player Controller ID: BP_PlayerController_AdminTools_C_2145312974)caused by BP_SCAR-H_Elcan_Suppressed_Black_C_2145123807
0111[2025.09.13-14.28.34:562][434]LogSquadTrace: [DedicatedServer]Wound(): Player: [DERI] M0H KillingDamage=33.500000 from BP_PlayerController_AdminTools_C_2145312974 (Online IDs: EOS: 00020eec51be45fca3025ff7c8b6cbf4 steam: ***************** | Controller ID: BP_PlayerController_AdminTools_C_2145312974) caused by BP_SCAR-H_Elcan_Suppressed_Black_C_2145123807
[2025.09.13-14.28.34:562][434]PIE: SATKillFeedLog: {"killer":{"name":"Z1PO","eos":"00020eec51be45fca3025ff7c8b6cbf4","damage_causer_loc":"-50691.2,39263.9,-7762.7"},"victim":{"name":"[DERI] M0H","eos":"000215fd35a34b5282b3964fc8a1c7fb","hit_loc":"-50663.0,38525.0,-7851.0"},"weapon":"BP_SCAR-H_Elcan_Suppressed_Black_C"}
0111[2025.09.13-14.28.34:843][452]LogSquadTrace: [DedicatedServer]OnPossess(): PC=SAFA7, (Online IDs: EOS: 00029827c932477ea455f1f62217301c steam: 76561198392441659) Pawn=BP_Kord_Cupola_T72S_GFI_Turret_C_2145131058 FullPath=BP_Kord_Cupola_T72S_GFI_Turret_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_Kord_Cupola_T72S_GFI_Turret_C_2145131058
0111[2025.09.13-14.28.34:843][452]LogSquadTrace: [DedicatedServer]OnUnPossess(): PC=SAFA7, (Online IDs: EOS: 00029827c932477ea455f1f62217301c steam: 76561198392441659) Exited Vehicle Pawn=SAFA7, (Asset Name=BP_T72S_GFI_C) FullPath=BP_T72S_GFI_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_T72S_GFI_C_2145131081 Seat Number=2
0111[2025.09.13-14.28.34:843][452]LogSquadTrace: [DedicatedServer]OnPossess(): PC=SAFA7, (Online IDs: EOS: 00029827c932477ea455f1f62217301c steam: 76561198392441659) Entered Vehicle Pawn=BP_Kord_Cupola_T72S_GFI_Turret_C_2145131058 (Asset Name = BP_T72S_GFI_C) FullPath=BP_Kord_Cupola_T72S_GFI_Turret_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_Kord_Cupola_T72S_GFI_Turret_C_2145131058 Seat Number=2
[2025.09.13-14.28.35:093][467]LogRedpointEOS: [LogEOSAntiCheat] [AntiCheatServer] [ClientAuthStatusChanged] ClientHandle: 0x6e7 Status: 1
[2025.09.13-14.28.35:093][467]LogRedpointEOSNetworkAuth: Verbose: Server authentication: 0002688b656847fe8aaf8c687e423976: Anti-Cheat verification status is now 'LocalAuthComplete'.
[2025.09.13-14.28.35:093][467]LogRedpointEOSAntiCheat: Verbose: Dedicated server Anti-Cheat: 0x70f0d5f3e810: NotifyClientAuthStatusChanged(ClientHandle: 1767, ClientAuthStatus: 1): Propagated event to OnPlayerAuthStatusChanged() handler.
[2025.09.13-14.28.35:897][518]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:42] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 193/500
0111[2025.09.13-14.28.35:960][522]LogSquadTrace: [DedicatedServer]TraceAndMessageClient(): SQVehicleSeat::TakeDamage[GenericDamage] BP_UH60_C_2145143354 for 136.941284 damage (type=SQDamageType_Collision)
0111[2025.09.13-14.28.35:961][522]LogSquadTrace: [DedicatedServer]TraceAndMessageClient(): st88lx: 136.94 damage taken by causer st88lx instigator (Online Ids: st88lx) EOS: 00024b8fc75644978820e8ef85164685 steam: ***************** health remaining 863.06
[2025.09.13-14.28.36:487][555]LogSquad: ADMIN COMMAND: Remote admin has warned player  Z1PO. Message was "WARNING: You have killed 3 teammates. At 6 teamkills you will be banned for 30 minutes." from RCON
0111[2025.09.13-14.28.37:041][591]LogSquadTrace: [DedicatedServer]OnPossess(): PC=SNG (Online IDs: EOS: 00023e261d9745899796929e02627ce0 steam: 76561198813317747) Pawn=BP_T72S_GFI_C_2145131081 FullPath=BP_T72S_GFI_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_T72S_GFI_C_2145131081
0111[2025.09.13-14.28.37:041][591]LogSquadTrace: [DedicatedServer]OnUnPossess(): PC=SNG (Online IDs: EOS: 00023e261d9745899796929e02627ce0 steam: 76561198813317747) Exited Vehicle Pawn=SNG (Asset Name=BP_T72S_GFI_Turret_C) FullPath=BP_T72S_GFI_Turret_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_T72S_GFI_Turret_C_2145131071 Seat Number=0
0111[2025.09.13-14.28.37:041][591]LogSquadTrace: [DedicatedServer]OnPossess(): PC=SNG (Online IDs: EOS: 00023e261d9745899796929e02627ce0 steam: 76561198813317747) Entered Vehicle Pawn=BP_T72S_GFI_C_2145131081 (Asset Name = BP_T72S_GFI_C) FullPath=BP_T72S_GFI_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_T72S_GFI_C_2145131081 Seat Number=0
[2025.09.13-14.28.37:333][609]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:43] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 217/500
[2025.09.13-14.28.38:567][688]PIE: SATLog: Hit: None
[2025.09.13-14.28.38:567][688]LogSquad: Player:  king_offline ActualDamage=0.000002 from   king_offline (Online IDs: EOS: 0002cc7913164e9f8f20566c67a414e7 steam: 76561198053706826 | Player Controller ID: BP_PlayerController_AdminTools_C_2145513914)caused by BP_Soldier_USSF_Medic_C_2145129271
[2025.09.13-14.28.38:778][701]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:45] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 241/500
[2025.09.13-14.28.39:836][768]LogSquad: USQGameState: Server Tick Rate: 63.01
[2025.09.13-14.28.40:206][791]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:46] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 265/500
0111[2025.09.13-14.28.40:216][792]LogSquadTrace: [DedicatedServer]ServerFireProjectileWithId_Implementation(): Reloading: FireProjectile() called while unable to fire: [38986.647509] CurrentState:Firing RemainingRounds:0 ReloadStartTime:-1000.000000 CachedReloadFinishTime:-1000.000000 IsOverheated:0
[2025.09.13-14.28.40:216][792]LogTemp: Warning: FireProjectile() called while unable to fire
[2025.09.13-14.28.40:248][794]LogSquad: Warning: ASQWeapon::DealDamage was called but there was no valid actor or component.
[2025.09.13-14.28.40:375][802]PIE: SATLog: Hit: None
[2025.09.13-14.28.40:375][802]LogSquad: Player: zakomohammed60 ActualDamage=0.000002 from  zakomohammed60 (Online IDs: EOS: 0002555752284f4a830ee6cf8929c456 steam: 76561199115779819 | Player Controller ID: BP_PlayerController_AdminTools_C_2145178115)caused by BP_Soldier_USSF_Medic_C_2145109724
[2025.09.13-14.28.40:835][831]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2145195665 accept
[2025.09.13-14.28.40:835][831]LogNet: NotifyAcceptingConnection accepted from: **************:13321
[2025.09.13-14.28.40:914][836]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2145195665 accept
[2025.09.13-14.28.40:914][836]LogNet: NotifyAcceptingConnection accepted from: **************:13321
[2025.09.13-14.28.40:914][836]LogNet: Server accepting post-challenge connection from: **************:13321
[2025.09.13-14.28.40:914][836]LogNet: RedpointEOSIpNetConnection_2145095699 setting maximum channels to: 32767
[2025.09.13-14.28.40:914][836]PacketHandlerLog: Loaded PacketHandler component: OnlineSubsystemSteam.SteamAuthComponentModuleInterface ()
[2025.09.13-14.28.40:914][836]PacketHandlerLog: Loaded PacketHandler component: AESGCMHandlerComponent ()
[2025.09.13-14.28.40:914][836]PacketHandlerLog: Loaded PacketHandler component: Engine.EngineHandlerComponentFactory (StatelessConnectHandlerComponent)
[2025.09.13-14.28.40:914][836]LogNet: NotifyAcceptedConnection: Name: OnlineBeaconHost_2145195665, TimeStamp: 09/13/25 14:28:40, [UNetConnection] RemoteAddr: **************:13321, Name: RedpointEOSIpNetConnection_2145095699, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.13-14.28.40:914][836]LogNet: AddClientConnection: Added client connection: [UNetConnection] RemoteAddr: **************:13321, Name: RedpointEOSIpNetConnection_2145095699, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.13-14.28.40:998][841]LogOnline: STEAM: AUTH HANDLER: Sending auth result to user ***************** with flag success? 1
[2025.09.13-14.28.41:095][847]LogNet: NotifyAcceptingChannel Control 0 server OnlineBeaconHost /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.OnlineBeaconHost_2145195665: Accepted
[2025.09.13-14.28.41:095][847]LogNet: Remote platform little endian=1
[2025.09.13-14.28.41:095][847]LogNet: This platform little endian=1
[2025.09.13-14.28.41:095][847]LogRedpointEOSNetworkAuth: Verbose: **************: connection: AutomaticEncryption: Starting...
[2025.09.13-14.28.41:095][847]LogRedpointEOSNetworkAuth: Warning: The dedicated server had no public/private signing keypair set, so the connection will not be automatically encrypted.
[2025.09.13-14.28.41:095][847]LogRedpointEOSNetworkAuth: Verbose: **************: connection: AutomaticEncryption: Finished successfully.
[2025.09.13-14.28.41:095][847]LogRedpointEOSNetworkAuth: Verbose: **************: connection: All phases finished successfully.
[2025.09.13-14.28.41:095][847]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095699]: Beacon Hello
[2025.09.13-14.28.41:191][853]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095699]: Client netspeed is 18000
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Verbose: 0002a121352e44ed8635ecedd6c25674: verification: IdTokenAuth: Starting...
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 0002a121352e44ed8635ecedd6c25674 because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Verbose: 0002a121352e44ed8635ecedd6c25674: verification: IdTokenAuth: Finished successfully.
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Verbose: 0002a121352e44ed8635ecedd6c25674: verification: All phases finished successfully.
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Verbose: 0002a121352e44ed8635ecedd6c25674/SQJoinBeaconClient: beacon: Immediately finishing as there are no phases to run.
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Verbose: 0002a121352e44ed8635ecedd6c25674/SQJoinBeaconClient: beacon: Finished successfully.
[2025.09.13-14.28.41:191][853]LogRedpointEOSNetworkAuth: Verbose: 0002a121352e44ed8635ecedd6c25674/SQJoinBeaconClient: beacon: All phases finished successfully.
[2025.09.13-14.28.41:191][853]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095699]: Beacon Join SQJoinBeaconClient RedpointEOS:0002a121352e44ed8635ecedd6c25674 (unauthenticated)
[2025.09.13-14.28.41:270][858]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095699]: Handshake complete.
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Queuing 1 users for resolution (GetUsers)
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Scheduled pending callback for resolution of 1 users.
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 0002a121352e44ed8635ecedd6c25674: Ready? No ''
[2025.09.13-14.28.41:270][858]LogRedpointEOSUserCache: Verbose: ProcessQueue: Querying 1 external account mappings.
[2025.09.13-14.28.41:448][868]LogRedpointEOS: Verbose: [LogEOSConnect] FConnectClient::CacheExternalAccountInfo - ProductUserId: 0002a121352e44ed8635ecedd6c25674, AccountType: 1, AccountId: *****************, DisplayName: <Redacted>
[2025.09.13-14.28.41:448][868]LogRedpointEOSUserCache: Verbose: ProcessQueue: Queried 1 external account mappings.
[2025.09.13-14.28.41:448][868]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing successfully retrieved external account mappings.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: ProcessQueue: There are no Epic Games accounts to query.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing is complete after external mappings query, flushing pending callbacks and scheduling more processes if needed.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 0002a121352e44ed8635ecedd6c25674: Ready? Yes ''
[2025.09.13-14.28.41:449][868]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): All entries resolved or errored, firing callback.
[2025.09.13-14.28.41:449][868]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.41:449][868]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.41:449][868]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.41:449][868]SQLogJoinBeaconHost: client 0002a121352e44ed8635ecedd6c25674 added to public queue. New Size: 1
[2025.09.13-14.28.41:634][880]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:48] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 289/500
[2025.09.13-14.28.41:915][898]SQLogJoinBeaconHost: RequestClientJoin: 0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.41:915][898]SQLogJoinBeaconHost: Removing from PublicQueue: 0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.42:003][904]LogNet: UChannel::ReceivedSequencedBunch: Bunch.bClose == true. ChIndex == 0. Calling ConditionalCleanUp.
[2025.09.13-14.28.42:003][904]LogNet: UChannel::CleanUp: ChIndex == 0. Closing connection. [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: **************:13321, Name: RedpointEOSIpNetConnection_2145095699, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095693, UniqueId: RedpointEOS:0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.42:003][904]LogNet: UNetConnection::Close: [UNetConnection] RemoteAddr: **************:13321, Name: RedpointEOSIpNetConnection_2145095699, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095693, UniqueId: RedpointEOS:0002a121352e44ed8635ecedd6c25674, Channels: 4, Time: 2025.09.13-14.28.42
[2025.09.13-14.28.42:003][904]LogNet: UNetConnection::SendCloseReason:
[2025.09.13-14.28.42:003][904]LogNet:  - Result=ControlChannelClose, ErrorContext="ControlChannelClose"
[2025.09.13-14.28.42:003][904]LogNet: UChannel::Close: Sending CloseBunch. ChIndex == 0. Name: [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: **************:13321, Name: RedpointEOSIpNetConnection_2145095699, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095693, UniqueId: RedpointEOS:0002a121352e44ed8635ecedd6c25674
[2025.09.13-14.28.42:019][905]LogNet: UNetDriver::RemoveClientConnection - Removed address **************:13321 from MappedClientConnections for: [UNetConnection] RemoteAddr: **************:13321, Name: RedpointEOSIpNetConnection_2145095699, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095693, UniqueId: RedpointEOS:0002a121352e44ed8635ecedd6c25674
0111[2025.09.13-14.28.43:032][969]LogSquadTrace: [DedicatedServer]OnPossess(): PC=st88lx (Online IDs: EOS: 00024b8fc75644978820e8ef85164685 steam: *****************) Pawn=BP_Soldier_GE_USA_CREW_C_2145175791 FullPath=BP_Soldier_GE_USA_CREW_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_Soldier_GE_USA_CREW_C_2145175791
0111[2025.09.13-14.28.43:032][969]LogSquadTrace: [DedicatedServer]OnUnPossess(): PC=st88lx (Online IDs: EOS: 00024b8fc75644978820e8ef85164685 steam: *****************) Exited Vehicle Pawn=st88lx (Asset Name=BP_UH60_C) FullPath=BP_UH60_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_UH60_C_2145143354 Seat Number=0
[2025.09.13-14.28.43:033][969]LogHelicopter: Warning: not found the matching length (300.000000)
[2025.09.13-14.28.43:033][969]LogSquad: Helicopter is too close to the ground, using normal seat exits
[2025.09.13-14.28.43:073][971]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:49] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 313/500
[2025.09.13-14.28.43:853][ 21]PIE: SATLog: Hit: None
[2025.09.13-14.28.43:853][ 21]LogSquad: Player:DAVID st88lx ActualDamage=0.000002 from DAVID st88lx (Online IDs: EOS: 00024b8fc75644978820e8ef85164685 steam: ***************** | Player Controller ID: BP_PlayerController_AdminTools_C_2145276783)caused by BP_Soldier_GE_USA_CREW_C_2145175791
[2025.09.13-14.28.44:026][ 32]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2145195665 accept
[2025.09.13-14.28.44:026][ 32]LogNet: NotifyAcceptingConnection accepted from: **************:6758
[2025.09.13-14.28.44:074][ 35]LogNet: NotifyAcceptingConnection: Server OnlineBeaconHost_2145195665 accept
[2025.09.13-14.28.44:074][ 35]LogNet: NotifyAcceptingConnection accepted from: **************:6758
[2025.09.13-14.28.44:074][ 35]LogNet: Server accepting post-challenge connection from: **************:6758
[2025.09.13-14.28.44:074][ 35]LogNet: RedpointEOSIpNetConnection_2145095673 setting maximum channels to: 32767
[2025.09.13-14.28.44:074][ 35]PacketHandlerLog: Loaded PacketHandler component: OnlineSubsystemSteam.SteamAuthComponentModuleInterface ()
[2025.09.13-14.28.44:074][ 35]PacketHandlerLog: Loaded PacketHandler component: AESGCMHandlerComponent ()
[2025.09.13-14.28.44:074][ 35]PacketHandlerLog: Loaded PacketHandler component: Engine.EngineHandlerComponentFactory (StatelessConnectHandlerComponent)
[2025.09.13-14.28.44:074][ 35]LogNet: NotifyAcceptedConnection: Name: OnlineBeaconHost_2145195665, TimeStamp: 09/13/25 14:28:44, [UNetConnection] RemoteAddr: **************:6758, Name: RedpointEOSIpNetConnection_2145095673, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.13-14.28.44:074][ 35]LogNet: AddClientConnection: Added client connection: [UNetConnection] RemoteAddr: **************:6758, Name: RedpointEOSIpNetConnection_2145095673, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: NULL, UniqueId: INVALID
[2025.09.13-14.28.44:121][ 38]LogOnline: STEAM: AUTH HANDLER: Sending auth result to user ***************** with flag success? 1
[2025.09.13-14.28.44:168][ 41]LogNet: NotifyAcceptingChannel Control 0 server OnlineBeaconHost /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.OnlineBeaconHost_2145195665: Accepted
[2025.09.13-14.28.44:168][ 41]LogNet: Remote platform little endian=1
[2025.09.13-14.28.44:168][ 41]LogNet: This platform little endian=1
[2025.09.13-14.28.44:168][ 41]LogRedpointEOSNetworkAuth: Verbose: **************: connection: AutomaticEncryption: Starting...
[2025.09.13-14.28.44:168][ 41]LogRedpointEOSNetworkAuth: Warning: The dedicated server had no public/private signing keypair set, so the connection will not be automatically encrypted.
[2025.09.13-14.28.44:168][ 41]LogRedpointEOSNetworkAuth: Verbose: **************: connection: AutomaticEncryption: Finished successfully.
[2025.09.13-14.28.44:168][ 41]LogRedpointEOSNetworkAuth: Verbose: **************: connection: All phases finished successfully.
[2025.09.13-14.28.44:168][ 41]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095673]: Beacon Hello
[2025.09.13-14.28.44:215][ 44]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095673]: Client netspeed is 18000
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Verbose: 0002460c1fbd4db78f5a408f709fd5af: verification: IdTokenAuth: Starting...
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Warning: Skipping verification of connecting user 0002460c1fbd4db78f5a408f709fd5af because this connection is not trusted. To verify users, turn on trusted dedicated servers.
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Verbose: 0002460c1fbd4db78f5a408f709fd5af: verification: IdTokenAuth: Finished successfully.
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Verbose: 0002460c1fbd4db78f5a408f709fd5af: verification: All phases finished successfully.
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Verbose: 0002460c1fbd4db78f5a408f709fd5af/SQJoinBeaconClient: beacon: Immediately finishing as there are no phases to run.
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Verbose: 0002460c1fbd4db78f5a408f709fd5af/SQJoinBeaconClient: beacon: Finished successfully.
[2025.09.13-14.28.44:215][ 44]LogRedpointEOSNetworkAuth: Verbose: 0002460c1fbd4db78f5a408f709fd5af/SQJoinBeaconClient: beacon: All phases finished successfully.
[2025.09.13-14.28.44:215][ 44]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095673]: Beacon Join SQJoinBeaconClient RedpointEOS:0002460c1fbd4db78f5a408f709fd5af (unauthenticated)
[2025.09.13-14.28.44:262][ 47]LogBeacon: OnlineBeaconHost_2145195665[RedpointEOSIpNetConnection_2145095673]: Handshake complete.
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Queuing 1 users for resolution (GetUsers)
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Scheduled pending callback for resolution of 1 users.
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 0002460c1fbd4db78f5a408f709fd5af: Ready? No ''
[2025.09.13-14.28.44:262][ 47]LogRedpointEOSUserCache: Verbose: ProcessQueue: Querying 1 external account mappings.
[2025.09.13-14.28.44:421][ 57]PIE: SATLog: Hit: None
[2025.09.13-14.28.44:421][ 57]LogSquad: Player:DAVID st88lx ActualDamage=0.000002 from DAVID st88lx (Online IDs: EOS: 00024b8fc75644978820e8ef85164685 steam: ***************** | Player Controller ID: BP_PlayerController_AdminTools_C_2145276783)caused by BP_Soldier_GE_USA_CREW_C_2145175791
[2025.09.13-14.28.44:448][ 58]LogRedpointEOS: Verbose: [LogEOSConnect] FConnectClient::CacheExternalAccountInfo - ProductUserId: 0002460c1fbd4db78f5a408f709fd5af, AccountType: 1, AccountId: *****************, DisplayName: <Redacted>
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: ProcessQueue: Queried 1 external account mappings.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing successfully retrieved external account mappings.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: ProcessQueue: There are no Epic Games accounts to query.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: ProcessQueue: Processing is complete after external mappings query, flushing pending callbacks and scheduling more processes if needed.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(epicAccountIdsByDisplayName): Got 0 pending callbacks remaining.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(userIdsByExternalAccountId): Got 0 pending callbacks remaining.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Got 1 pending callbacks remaining.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0 has 1 entries to check.
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): Pending callback #0, entry 0002460c1fbd4db78f5a408f709fd5af: Ready? Yes ''
[2025.09.13-14.28.44:448][ 58]LogRedpointEOSUserCache: Verbose: Queue_Templated_FlushPendingCallbacks(users): All entries resolved or errored, firing callback.
[2025.09.13-14.28.44:448][ 58]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:448][ 58]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:448][ 58]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:448][ 58]SQLogJoinBeaconHost: client 0002460c1fbd4db78f5a408f709fd5af added to public queue. New Size: 1
[2025.09.13-14.28.44:506][ 62]LogBlueprintUserMessages: [BP_EntrenchingTool_Engineer_New_C_2145174023] [10:49:50] BP_EntrenchingTool_Engineer_New_C_2145174023: Shovel dealt damage: GFI_Hab_C_2145097449. Current HP: 337/500
[2025.09.13-14.28.44:608][ 69]LogBazaarSubsystem: [GetInventoryItems] GetInventoryItems Started for PlayerID: 00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.44:750][ 78]PIE: SATLog: Hit: None
[2025.09.13-14.28.44:750][ 78]LogSquad: Player:[TF]Omr Nightbringer ActualDamage=0.000002 from [TF]Omr Nightbringer (Online IDs: EOS: 0002f96124274ed8a8c2dec6e5f61b23 steam: 76561199111092700 | Player Controller ID: BP_PlayerController_AdminTools_C_2145243111)caused by BP_Soldier_USSF_SL_C_2145098355
[2025.09.13-14.28.44:858][ 84]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:0002af484cf440c48ea2f035ccac642d
[2025.09.13-14.28.44:858][ 84]LogSquadCommon: SQCommonStatics Check Permissions, UniqueId:00026d06efcb4f7fa024899b2b0647ea
[2025.09.13-14.28.44:858][ 84]LogBazaarSubsystem: [OnGetInventorySuccess] GetInventoryItems Success. Got []
[2025.09.13-14.28.44:912][ 88]SQLogJoinBeaconHost: RequestClientJoin: 0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:912][ 88]SQLogJoinBeaconHost: Removing from PublicQueue: 0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:956][ 91]LogNet: UChannel::ReceivedSequencedBunch: Bunch.bClose == true. ChIndex == 0. Calling ConditionalCleanUp.
[2025.09.13-14.28.44:956][ 91]LogNet: UChannel::CleanUp: ChIndex == 0. Closing connection. [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: **************:6758, Name: RedpointEOSIpNetConnection_2145095673, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095667, UniqueId: RedpointEOS:0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:956][ 91]LogNet: UNetConnection::Close: [UNetConnection] RemoteAddr: **************:6758, Name: RedpointEOSIpNetConnection_2145095673, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095667, UniqueId: RedpointEOS:0002460c1fbd4db78f5a408f709fd5af, Channels: 4, Time: 2025.09.13-14.28.44
[2025.09.13-14.28.44:956][ 91]LogNet: UNetConnection::SendCloseReason:
[2025.09.13-14.28.44:956][ 91]LogNet:  - Result=ControlChannelClose, ErrorContext="ControlChannelClose"
[2025.09.13-14.28.44:956][ 91]LogNet: UChannel::Close: Sending CloseBunch. ChIndex == 0. Name: [UChannel] ChIndex: 0, Closing: 0 [UNetConnection] RemoteAddr: **************:6758, Name: RedpointEOSIpNetConnection_2145095673, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095667, UniqueId: RedpointEOS:0002460c1fbd4db78f5a408f709fd5af
[2025.09.13-14.28.44:973][ 92]LogNet: UNetDriver::RemoveClientConnection - Removed address **************:6758 from MappedClientConnections for: [UNetConnection] RemoteAddr: **************:6758, Name: RedpointEOSIpNetConnection_2145095673, Driver: Name:RedpointEOSNetDriver_2145195664 Def:BeaconNetDriver RedpointEOSNetDriver_2145195664, IsServer: YES, PC: NULL, Owner: SQJoinBeaconClient_2145095667, UniqueId: RedpointEOS:0002460c1fbd4db78f5a408f709fd5af
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]RestartPlayer(): On Server PC=szmark01 Spawn=BP_ForwardBaseSpawn_C_2145122732 DeployRole=USSF_SL_01
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]FindPlayerStart_Implementation(): On Server PC=szmark01 Spawn=BP_ForwardBaseSpawn_C_2145122732 DeployRole=USSF_SL_01 IncomingName=
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server PC=szmark01 Spawn=BP_ForwardBaseSpawn_C_2145122732 DeployRole=USSF_SL_01
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]IsSpawnpointAllowed(): On Server GameStartTeamID=1 PSTeamID=1 CanSpawn()=1
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=szmark01
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=szmark01 ConcretePawnClassForController=BP_Soldier_USSF_Rifleman1_C
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]SpawnDefaultPawnFor_Implementation(): On Server PC=szmark01 DeployRole=USSF_SL_01
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=szmark01
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=szmark01 ConcretePawnClassForController=BP_Soldier_USSF_SL_C
[2025.09.13-14.28.45:053][ 97]LogSquad: Found a valid cached spawn location at V(X=3914.22, Y=13187.35, Z=-7126.47) for BP_ForwardBaseSpawn_C_2145122732
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=szmark01
0111[2025.09.13-14.28.45:053][ 97]LogSquadTrace: [DedicatedServer]GetDefaultPawnClassForController_Implementation(): On Server PC=szmark01 ConcretePawnClassForController=BP_Soldier_USSF_SL_C
0111[2025.09.13-14.28.45:054][ 97]LogSquadTrace: [DedicatedServer]OnPossess(): PC=szmark01 (Online IDs: EOS: 0002d09bf9754440976caf51fac3527c steam: 76561198120917024) Pawn=BP_Soldier_USSF_SL_C_2145095473 FullPath=BP_Soldier_USSF_SL_C /Game/Maps/Tallil_Outskirts/Gameplay_Layers/Tallil_Invasion_v1.Tallil_Invasion_v1:PersistentLevel.BP_Soldier_USSF_SL_C_2145095473
0111[2025.09.13-14.28.45:055][ 97]LogSquadTrace: [DedicatedServer]ChangeState(): PC=szmark01 (Online IDs: EOS: 0002d09bf9754440976caf51fac3527c steam: 76561198120917024) OldState=Inactive NewState=Playing