import { useEffect, useRef } from 'react';
import { io, Socket } from 'socket.io-client';
import { queryClient } from '@/lib/queryClient';

export function useSocket() {
  const socketRef = useRef<Socket | null>(null);

  useEffect(() => {
    // Connect to Socket.IO server
    const socket = io('/', {
      transports: ['websocket', 'polling']
    });

    socketRef.current = socket;

    // Handle connection events
    socket.on('connect', () => {
      console.log('Connected to WebSocket server:', socket.id);
    });

    socket.on('disconnect', () => {
      console.log('Disconnected from WebSocket server');
    });

    // Handle real-time events
    socket.on('newEvent', (event) => {
      console.log('New event received:', event);
      // Invalidate events query to refresh the kill feed
      queryClient.invalidateQueries({ queryKey: ['/api/events'] });
      
      // Also invalidate dashboard summary for updated stats
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/summary'] });
    });

    socket.on('playerUpdate', (playerData) => {
      console.log('Player update received:', playerData);
      // Invalidate players query to refresh player list
      queryClient.invalidateQueries({ queryKey: ['/api/players'] });
      
      // Invalidate server info for updated player count
      queryClient.invalidateQueries({ queryKey: ['/api/server'] });
    });

    socket.on('statsUpdate', (statsData) => {
      console.log('Stats update received:', statsData);
      // Invalidate leaderboards to refresh rankings
      queryClient.invalidateQueries({ queryKey: ['/api/leaderboard'] });
      
      // Invalidate dashboard summary for updated stats
      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/summary'] });
    });

    // Cleanup on unmount
    return () => {
      socket.disconnect();
    };
  }, []);

  return socketRef.current;
}