import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Trophy, Medal, Award } from "lucide-react";

interface LeaderboardEntry {
  rank: number;
  name: string;
  steamId: string;
  value: number;
  avatar?: string;
}

interface LeaderboardProps {
  title: string;
  entries: LeaderboardEntry[];
  metric: string;
  period: "week" | "month" | "all";
}

export default function Leaderboard({ title, entries, metric, period }: LeaderboardProps) {
  const getRankIcon = (rank: number) => {
    switch (rank) {
      case 1: return <Trophy className="w-4 h-4 text-yellow-500" />;
      case 2: return <Medal className="w-4 h-4 text-gray-400" />;
      case 3: return <Award className="w-4 h-4 text-amber-600" />;
      default: return <span className="w-4 h-4 text-center text-xs font-bold text-muted-foreground">{rank}</span>;
    }
  };

  const getPeriodLabel = () => {
    switch (period) {
      case "week": return "Last 7 days";
      case "month": return "Last 30 days";
      case "all": return "All time";
    }
  };

  return (
    <Card data-testid={`card-leaderboard-${metric}-${period}`} className="hover-elevate">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg" data-testid="text-leaderboard-title">{title}</CardTitle>
          <Badge variant="secondary" data-testid="badge-leaderboard-period">
            {getPeriodLabel()}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {entries.map((entry) => (
          <div
            key={`${entry.steamId}-${entry.rank}`}
            data-testid={`row-leaderboard-${entry.rank}`}
            className="flex items-center gap-3 p-2 rounded-md hover-elevate"
          >
            <div className="flex-shrink-0">
              {getRankIcon(entry.rank)}
            </div>
            
            <Avatar className="w-8 h-8">
              <AvatarImage src={entry.avatar} alt={entry.name} />
              <AvatarFallback className="text-xs">
                {entry.name.substring(0, 2).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <p className="font-medium truncate" data-testid={`text-player-name-${entry.rank}`}>
                {entry.name}
              </p>
              <p className="text-xs text-muted-foreground font-mono" data-testid={`text-steamid-${entry.rank}`}>
                {entry.steamId.substring(0, 12)}...
              </p>
            </div>
            
            <div className="text-right">
              <p className="font-bold" data-testid={`text-value-${entry.rank}`}>
                {entry.value}
              </p>
              <p className="text-xs text-muted-foreground">{metric}</p>
            </div>
          </div>
        ))}
      </CardContent>
    </Card>
  );
}