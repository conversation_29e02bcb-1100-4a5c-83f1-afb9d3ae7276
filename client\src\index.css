@tailwind base;
@tailwind components;
@tailwind utilities;

/* LIGHT MODE */
:root {
  --button-outline: rgba(0,0,0, .10);
  --badge-outline: rgba(0,0,0, .05);

  /* Automatic computation of border around primary / danger buttons */
  --opaque-button-border-intensity: -8; /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(0,0,0, .03);
  --elevate-2: rgba(0,0,0, .08);

  --background: 220 20% 97%;

  --foreground: 220 15% 15%;

  --border: 220 15% 88%;

  --card: 220 15% 100%;

  --card-foreground: 220 15% 15%;

  --card-border: 220 15% 93%;

  --sidebar: 220 18% 95%;

  --sidebar-foreground: 220 15% 15%;

  --sidebar-border: 220 15% 90%;

  --sidebar-primary: 200 90% 45%;

  --sidebar-primary-foreground: 220 15% 98%;

  --sidebar-accent: 220 15% 92%;

  --sidebar-accent-foreground: 220 15% 15%;

  --sidebar-ring: 200 90% 45%;

  --popover: 220 15% 98%;

  --popover-foreground: 220 15% 15%;

  --popover-border: 220 15% 90%;

  --primary: 200 90% 45%;

  --primary-foreground: 220 15% 98%;

  --secondary: 220 15% 94%;

  --secondary-foreground: 220 15% 15%;

  --muted: 220 12% 96%;

  --muted-foreground: 220 10% 45%;

  --accent: 220 10% 94%;

  --accent-foreground: 220 15% 15%;

  --destructive: 0 70% 55%;

  --destructive-foreground: 0 0% 98%;

  --input: 220 15% 75%;
  --ring: 200 90% 45%;
  --chart-1: 200 90% 35%;
  --chart-2: 120 60% 40%;
  --chart-3: 45 100% 50%;
  --chart-4: 280 70% 45%;
  --chart-5: 15 80% 45%;

  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: .5rem; /* 8px */
  --shadow-2xs: 0px 2px 0px 0px hsl(220 15% 88% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(220 15% 88% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 1px 2px -1px hsl(220 15% 88% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 1px 2px -1px hsl(220 15% 88% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 2px 4px -1px hsl(220 15% 88% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 4px 6px -1px hsl(220 15% 88% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 8px 10px -1px hsl(220 15% 88% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(220 15% 88% / 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* Automatically computed borders - intensity can be controlled by the user by the --opaque-button-border-intensity setting */

  /* Fallback for older browsers */
  --sidebar-primary-border: hsl(var(--sidebar-primary));
  --sidebar-primary-border: hsl(from hsl(var(--sidebar-primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --sidebar-accent-border: hsl(var(--sidebar-accent));
  --sidebar-accent-border: hsl(from hsl(var(--sidebar-accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --primary-border: hsl(var(--primary));
  --primary-border: hsl(from hsl(var(--primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --secondary-border: hsl(var(--secondary));
  --secondary-border: hsl(from hsl(var(--secondary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --muted-border: hsl(var(--muted));
  --muted-border: hsl(from hsl(var(--muted)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --accent-border: hsl(var(--accent));
  --accent-border: hsl(from hsl(var(--accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --destructive-border: hsl(var(--destructive));
  --destructive-border: hsl(from hsl(var(--destructive)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
}

.dark {
  --button-outline: rgba(255,255,255, .10);
  --badge-outline: rgba(255,255,255, .05);

  --opaque-button-border-intensity: 9;  /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(255,255,255, .04);
  --elevate-2: rgba(255,255,255, .09);

  --background: 220 15% 8%;

  --foreground: 220 15% 95%;

  --border: 220 12% 18%;

  --card: 220 12% 12%;

  --card-foreground: 220 15% 95%;

  --card-border: 220 10% 20%;

  --sidebar: 220 15% 10%;

  --sidebar-foreground: 220 15% 95%;

  --sidebar-border: 220 12% 18%;

  --sidebar-primary: 200 100% 60%;

  --sidebar-primary-foreground: 220 15% 8%;

  --sidebar-accent: 220 10% 16%;

  --sidebar-accent-foreground: 220 15% 90%;

  --sidebar-ring: 200 100% 60%;

  --popover: 220 10% 16%;

  --popover-foreground: 220 15% 95%;

  --popover-border: 220 10% 24%;

  --primary: 200 100% 60%;

  --primary-foreground: 220 15% 8%;

  --secondary: 220 10% 20%;

  --secondary-foreground: 220 15% 90%;

  --muted: 220 8% 14%;

  --muted-foreground: 220 10% 70%;

  --accent: 220 8% 16%;

  --accent-foreground: 220 15% 90%;

  --destructive: 0 70% 55%;

  --destructive-foreground: 0 0% 98%;

  --input: 220 10% 25%;
  --ring: 200 100% 60%;
  --chart-1: 200 100% 70%;
  --chart-2: 120 60% 60%;
  --chart-3: 45 100% 65%;
  --chart-4: 280 70% 65%;
  --chart-5: 15 80% 65%;

  --shadow-2xs: 0px 2px 0px 0px hsl(220 15% 5% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(220 15% 5% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 1px 2px -1px hsl(220 15% 5% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 1px 2px -1px hsl(220 15% 5% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 2px 4px -1px hsl(220 15% 5% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 4px 6px -1px hsl(220 15% 5% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 8px 10px -1px hsl(220 15% 5% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(220 15% 5% / 0.00);

}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/**
 * Using the elevate system.
 * Automatic contrast adjustment.
 *
 * <element className="hover-elevate" />
 * <element className="active-elevate-2" />
 *
 * // Using the tailwind utility when a data attribute is "on"
 * <element className="toggle-elevate data-[state=on]:toggle-elevated" />
 * // Or manually controlling the toggle state
 * <element className="toggle-elevate toggle-elevated" />
 *
 * Elevation systems have to handle many states.
 * - not-hovered, vs. hovered vs. active  (three mutually exclusive states)
 * - toggled or not
 * - focused or not (this is not handled with these utilities)
 *
 * Even without handling focused or not, this is six possible combinations that
 * need to be distinguished from eachother visually.
 */
@layer utilities {

  /* Hide ugly search cancel button in Chrome until we can style it properly */
  input[type="search"]::-webkit-search-cancel-button {
    @apply hidden;
  }

  /* Placeholder styling for contentEditable div */
  [contenteditable][data-placeholder]:empty::before {
    content: attr(data-placeholder);
    color: hsl(var(--muted-foreground));
    pointer-events: none;
  }

  /* .no-default-hover-elevate/no-default-active-elevate is an escape hatch so consumers of
   * buttons/badges can remove the automatic brightness adjustment on interactions
   * and program their own. */
  .no-default-hover-elevate {}

  .no-default-active-elevate {}


  /**
   * Toggleable backgrounds go behind the content. Hoverable/active goes on top.
   * This way they can stack/compound. Both will overlap the parent's borders!
   * So borders will be automatically adjusted both on toggle, and hover/active,
   * and they will be compounded.
   */
  .toggle-elevate::before,
  .toggle-elevate-2::before {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0px;
    /*border-radius: inherit;   match rounded corners */
    border-radius: inherit;
    z-index: -1;
    /* sits behind content but above backdrop */
  }

  .toggle-elevate.toggle-elevated::before {
    background-color: var(--elevate-2);
  }

  /* If there's a 1px border, adjust the inset so that it covers that parent's border */
  .border.toggle-elevate::before {
    inset: -1px;
  }

  /* Does not work on elements with overflow:hidden! */
  .hover-elevate:not(.no-default-hover-elevate),
  .active-elevate:not(.no-default-active-elevate),
  .hover-elevate-2:not(.no-default-hover-elevate),
  .active-elevate-2:not(.no-default-active-elevate) {
    position: relative;
    z-index: 0;
  }

  .hover-elevate:not(.no-default-hover-elevate)::after,
  .active-elevate:not(.no-default-active-elevate)::after,
  .hover-elevate-2:not(.no-default-hover-elevate)::after,
  .active-elevate-2:not(.no-default-active-elevate)::after {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0px;
    /*border-radius: inherit;   match rounded corners */
    border-radius: inherit;
    z-index: 999;
    /* sits in front of content */
  }

  .hover-elevate:hover:not(.no-default-hover-elevate)::after,
  .active-elevate:active:not(.no-default-active-elevate)::after {
    background-color: var(--elevate-1);
  }

  .hover-elevate-2:hover:not(.no-default-hover-elevate)::after,
  .active-elevate-2:active:not(.no-default-active-elevate)::after {
    background-color: var(--elevate-2);
  }

  /* If there's a 1px border, adjust the inset so that it covers that parent's border */
  .border.hover-elevate:not(.no-hover-interaction-elevate)::after,
  .border.active-elevate:not(.no-active-interaction-elevate)::after,
  .border.hover-elevate-2:not(.no-hover-interaction-elevate)::after,
  .border.active-elevate-2:not(.no-active-interaction-elevate)::after,
  .border.hover-elevate:not(.no-hover-interaction-elevate)::after {
    inset: -1px;
  }
}