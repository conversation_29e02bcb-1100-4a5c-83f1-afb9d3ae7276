import StatsCard from '../StatsCard';
import { <PERSON>, <PERSON>, Heart, Target } from "lucide-react";

export default function StatsCardExample() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <StatsCard
        title="Active Players"
        value={42}
        subtitle="of 96 max"
        trend="up"
        trendValue="+5"
        icon={<Users className="w-4 h-4" />}
        variant="positive"
      />
      <StatsCard
        title="Total Kills"
        value="1,248"
        subtitle="last 24h"
        trend="stable"
        trendValue="0%"
        icon={<Skull className="w-4 h-4" />}
      />
      <StatsCard
        title="Revives"
        value={345}
        subtitle="last 24h"
        trend="up"
        trendValue="+12%"
        icon={<Heart className="w-4 h-4" />}
        variant="positive"
      />
      <StatsCard
        title="Server Uptime"
        value="98.5%"
        subtitle="last 30 days"
        trend="down"
        trendValue="-0.3%"
        icon={<Target className="w-4 h-4" />}
        variant="neutral"
      />
    </div>
  );
}