import BasePlugin from './base-plugin.js';
import axios from 'axios';

export default class SquadStats extends BasePlugin {
  static get description() {
    return 'Squad Statistics Dashboard Plugin - Sends game events to external dashboard API for real-time monitoring and statistics';
  }

  static get defaultEnabled() {
    return false;
  }

  static get optionsSpecification() {
    return {
      apiBaseUrl: {
        required: true,
        description: 'Base URL of the dashboard API (e.g., https://your-dashboard-url)'
      },
      apiTimeout: {
        required: false,
        description: 'API request timeout in milliseconds',
        default: 5000
      },
      serverId: {
        required: false,
        description: 'Unique server identifier for multi-server deployments',
        default: 1
      },
      enableDebugLogging: {
        required: false,
        description: 'Enable debug logging for troubleshooting',
        default: false
      },
      retryAttempts: {
        required: false,
        description: 'Number of retry attempts for failed API calls',
        default: 3
      }
    };
  }

  constructor(server, options, connectors) {
    super(server, options, connectors);
    
    // HTTP client configuration
    this.apiClient = axios.create({
      baseURL: this.options.apiBaseUrl,
      timeout: this.options.apiTimeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'SquadJS-SquadStats/1.0.0'
      }
    });

    // Event handler bindings
    this.onPlayerConnected = this.onPlayerConnected.bind(this);
    this.onPlayerDisconnected = this.onPlayerDisconnected.bind(this);
    this.onPlayerDied = this.onPlayerDied.bind(this);
    this.onPlayerRevived = this.onPlayerRevived.bind(this);
    this.onPlayerSpawn = this.onPlayerSpawn.bind(this);
    this.onNewGame = this.onNewGame.bind(this);
    this.onRoundEnded = this.onRoundEnded.bind(this);
  }

  async mount() {
    this.verbose(1, 'Mounting Squad Stats plugin...');
    
    // Test API connectivity
    try {
      await this.testApiConnection();
      this.verbose(1, 'Dashboard API connection successful');
    } catch (error) {
      this.verbose(1, `Warning: Cannot connect to dashboard API: ${error.message}`);
    }

    // Register event listeners using correct SquadJS event names
    this.server.on('PLAYER_CONNECTED', this.onPlayerConnected);
    this.server.on('PLAYER_DISCONNECTED', this.onPlayerDisconnected);
    this.server.on('PLAYER_DIED', this.onPlayerDied);
    this.server.on('PLAYER_REVIVED', this.onPlayerRevived);
    this.server.on('PLAYER_SPAWN', this.onPlayerSpawn);
    this.server.on('NEW_GAME', this.onNewGame);
    this.server.on('ROUND_ENDED', this.onRoundEnded);

    this.verbose(1, 'Squad Stats plugin mounted successfully');
  }

  async unmount() {
    this.verbose(1, 'Unmounting Squad Stats plugin...');
    
    // Remove event listeners
    this.server.off('PLAYER_CONNECTED', this.onPlayerConnected);
    this.server.off('PLAYER_DISCONNECTED', this.onPlayerDisconnected);
    this.server.off('PLAYER_DIED', this.onPlayerDied);
    this.server.off('PLAYER_REVIVED', this.onPlayerRevived);
    this.server.off('PLAYER_SPAWN', this.onPlayerSpawn);
    this.server.off('NEW_GAME', this.onNewGame);
    this.server.off('ROUND_ENDED', this.onRoundEnded);

    this.verbose(1, 'Squad Stats plugin unmounted');
  }

  async testApiConnection() {
    const response = await this.apiClient.get('/api/server');
    return response.status === 200;
  }

  async sendEventToAPI(eventData, attemptNumber = 1) {
    try {
      const response = await this.apiClient.post('/api/events', eventData);
      
      if (this.options.enableDebugLogging) {
        this.verbose(3, `Event sent successfully: ${eventData.type} (ID: ${response.data.event?.id})`);
      }
      
      return response.data;
    } catch (error) {
      this.verbose(1, `Failed to send event to dashboard API (attempt ${attemptNumber}): ${error.message}`);
      
      // Retry logic for failed requests with exponential backoff
      if (error.response?.status >= 500 && attemptNumber <= this.options.retryAttempts) {
        const backoffMs = Math.min(1000 * Math.pow(2, attemptNumber - 1), 10000);
        this.verbose(2, `Retrying API call in ${backoffMs}ms (attempt ${attemptNumber + 1}/${this.options.retryAttempts + 1})`);
        
        return new Promise((resolve, reject) => {
          setTimeout(async () => {
            try {
              const result = await this.sendEventToAPI(eventData, attemptNumber + 1);
              resolve(result);
            } catch (retryError) {
              reject(retryError);
            }
          }, backoffMs);
        });
      }
      
      throw error;
    }
  }

  // Event Handlers
  async onPlayerConnected(info) {
    try {
      await this.sendEventToAPI({
        type: 'join',
        actorSteamId: info.player.steamID,
        serverId: this.options.serverId,
        meta: {
          map: info.player.currentLayer || 'Unknown',
          playerName: info.player.name,
          eosId: info.player.eosID,
          teamId: info.player.teamID,
          squadId: info.player.squadID
        },
        playerData: {
          displayName: info.player.name,
          eosId: info.player.eosID
        }
      });

      this.verbose(2, `Player connected: ${info.player.name} (${info.player.steamID})`);
    } catch (error) {
      this.verbose(1, `Error handling player connection: ${error.message}`);
    }
  }

  async onPlayerDisconnected(info) {
    try {
      await this.sendEventToAPI({
        type: 'leave',
        actorSteamId: info.player.steamID,
        serverId: this.options.serverId,
        meta: {
          map: info.player.currentLayer || 'Unknown',
          playerName: info.player.name,
          eosId: info.player.eosID
        }
      });

      this.verbose(2, `Player disconnected: ${info.player.name} (${info.player.steamID})`);
    } catch (error) {
      this.verbose(1, `Error handling player disconnection: ${error.message}`);
    }
  }

  async onPlayerDied(info) {
    try {
      // Handle both regular kills and suicides/teamkills
      const attackerSteamId = info.attacker?.steamID || null;
      const victimSteamId = info.victim?.steamID;

      await this.sendEventToAPI({
        type: 'kill',
        actorSteamId: attackerSteamId,
        targetSteamId: victimSteamId,
        serverId: this.options.serverId,
        meta: {
          map: info.victim?.currentLayer || 'Unknown',
          weapon: info.weapon || 'Unknown',
          headshot: info.headshot || false,
          killerName: info.attacker?.name || null,
          victimName: info.victim?.name,
          killerEOS: info.attacker?.eosID || null,
          victimEOS: info.victim?.eosID,
          teamkill: info.teamkill || false,
          suicide: !info.attacker || info.attacker.steamID === info.victim.steamID
        }
      });

      const logMessage = attackerSteamId 
        ? `Kill: ${info.attacker?.name} -> ${info.victim?.name} (${info.weapon})`
        : `Death: ${info.victim?.name} (suicide/environment)`;
      
      this.verbose(2, logMessage);
    } catch (error) {
      this.verbose(1, `Error handling player death: ${error.message}`);
    }
  }

  async onPlayerRevived(info) {
    try {
      await this.sendEventToAPI({
        type: 'revive',
        actorSteamId: info.reviver?.steamID,
        targetSteamId: info.victim?.steamID,
        serverId: this.options.serverId,
        meta: {
          map: info.victim?.currentLayer || 'Unknown',
          reviverName: info.reviver?.name,
          victimName: info.victim?.name,
          reviverEOS: info.reviver?.eosID,
          victimEOS: info.victim?.eosID
        }
      });

      this.verbose(2, `Revive: ${info.reviver?.name} revived ${info.victim?.name}`);
    } catch (error) {
      this.verbose(1, `Error handling player revive: ${error.message}`);
    }
  }

  async onPlayerSpawn(info) {
    try {
      await this.sendEventToAPI({
        type: 'respawn',
        actorSteamId: info.player?.steamID,
        serverId: this.options.serverId,
        meta: {
          map: info.player?.currentLayer || 'Unknown',
          playerName: info.player?.name,
          eosId: info.player?.eosID,
          teamId: info.player?.teamID,
          squadId: info.player?.squadID,
          role: info.player?.role
        }
      });

      this.verbose(3, `Player spawned: ${info.player?.name}`);
    } catch (error) {
      this.verbose(1, `Error handling player spawn: ${error.message}`);
    }
  }

  async onNewGame(info) {
    try {
      await this.sendEventToAPI({
        type: 'round_start',
        serverId: this.options.serverId,
        meta: {
          map: info.layer || info.currentLayer || 'Unknown',
          gamemode: info.gamemode || 'Unknown'
        }
      });

      this.verbose(2, `Round started: ${info.layer || 'Unknown'}`);
    } catch (error) {
      this.verbose(1, `Error handling round start: ${error.message}`);
    }
  }

  async onRoundEnded(info) {
    try {
      await this.sendEventToAPI({
        type: 'round_end',
        serverId: this.options.serverId,
        meta: {
          map: info.layer || info.currentLayer || 'Unknown',
          winner: info.winner || 'Unknown',
          duration: info.duration || 0
        }
      });

      this.verbose(2, `Round ended: ${info.layer || 'Unknown'} - Winner: ${info.winner || 'Unknown'}`);
    } catch (error) {
      this.verbose(1, `Error handling round end: ${error.message}`);
    }
  }

  verbose(level, message) {
    if (this.server.logger) {
      this.server.logger(level, 'SquadStats', message);
    } else {
      console.log(`[SquadStats] ${message}`);
    }
  }
}