import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { ExternalLink, Skull, Heart, Target } from "lucide-react";

interface PlayerCardProps {
  name: string;
  steamId: string;
  squad?: string;
  role?: string;
  score: number;
  kills: number;
  deaths: number;
  revives: number;
  status: "online" | "offline";
  avatar?: string;
  onSteamLink?: () => void;
  onViewStats?: () => void;
}

export default function PlayerCard({
  name,
  steamId,
  squad,
  role,
  score,
  kills,
  deaths,
  revives,
  status,
  avatar,
  onSteamLink,
  onViewStats
}: PlayerCardProps) {
  const kd = deaths > 0 ? (kills / deaths).toFixed(2) : kills.toString();
  const statusColor = status === "online" ? "bg-green-500" : "bg-gray-500";

  return (
    <Card data-testid={`card-player-${steamId}`} className="p-4 hover-elevate">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3 flex-1 min-w-0">
          <div className="relative">
            <Avatar className="w-10 h-10">
              <AvatarImage src={avatar} alt={name} />
              <AvatarFallback>{name.substring(0, 2).toUpperCase()}</AvatarFallback>
            </Avatar>
            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-card ${statusColor}`} />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold truncate" data-testid={`text-player-name-${steamId}`}>
                {name}
              </h3>
              {squad && (
                <Badge variant="outline" className="text-xs" data-testid={`badge-squad-${steamId}`}>
                  {squad}
                </Badge>
              )}
            </div>
            <div className="flex items-center gap-4 text-xs text-muted-foreground">
              <span className="font-mono" data-testid={`text-steamid-${steamId}`}>
                {steamId.substring(0, 8)}...
              </span>
              {role && <span>{role}</span>}
              <span data-testid={`text-score-${steamId}`}>Score: {score}</span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="flex gap-3 text-sm">
            <div className="flex items-center gap-1" data-testid={`stat-kills-${steamId}`}>
              <Skull className="w-3 h-3 text-red-500" />
              <span>{kills}</span>
            </div>
            <div className="flex items-center gap-1" data-testid={`stat-deaths-${steamId}`}>
              <Target className="w-3 h-3 text-gray-500" />
              <span>{deaths}</span>
            </div>
            <div className="flex items-center gap-1" data-testid={`stat-revives-${steamId}`}>
              <Heart className="w-3 h-3 text-green-500" />
              <span>{revives}</span>
            </div>
            <div className="text-muted-foreground" data-testid={`stat-kd-${steamId}`}>
              K/D: {kd}
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={onSteamLink}
              data-testid={`button-steam-link-${steamId}`}
            >
              <ExternalLink className="w-3 h-3" />
            </Button>
            <Button
              size="sm"
              onClick={onViewStats}
              data-testid={`button-view-stats-${steamId}`}
            >
              Stats
            </Button>
          </div>
        </div>
      </div>
    </Card>
  );
}