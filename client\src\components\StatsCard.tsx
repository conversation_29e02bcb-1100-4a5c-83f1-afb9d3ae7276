import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";

interface StatsCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  trend?: "up" | "down" | "stable";
  trendValue?: string;
  icon?: React.ReactNode;
  variant?: "default" | "positive" | "negative" | "neutral";
}

export default function StatsCard({
  title,
  value,
  subtitle,
  trend,
  trendValue,
  icon,
  variant = "default"
}: StatsCardProps) {
  const getTrendIcon = () => {
    switch (trend) {
      case "up": return <TrendingUp className="w-3 h-3" />;
      case "down": return <TrendingDown className="w-3 h-3" />;
      case "stable": return <Minus className="w-3 h-3" />;
      default: return null;
    }
  };

  const getTrendColor = () => {
    switch (trend) {
      case "up": return "text-green-600";
      case "down": return "text-red-600";
      case "stable": return "text-gray-500";
      default: return "text-muted-foreground";
    }
  };

  const getVariantStyles = () => {
    switch (variant) {
      case "positive": return "border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-900/20";
      case "negative": return "border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-900/20";
      case "neutral": return "border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-900/20";
      default: return "";
    }
  };

  return (
    <Card data-testid={`card-stats-${title.toLowerCase().replace(/\s+/g, '-')}`} className={`hover-elevate ${getVariantStyles()}`}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium" data-testid="text-stats-title">
          {title}
        </CardTitle>
        {icon && <div className="text-muted-foreground">{icon}</div>}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold mb-1" data-testid="text-stats-value">
          {value}
        </div>
        <div className="flex items-center justify-between">
          {subtitle && (
            <p className="text-xs text-muted-foreground" data-testid="text-stats-subtitle">
              {subtitle}
            </p>
          )}
          {trend && trendValue && (
            <div className={`flex items-center gap-1 text-xs ${getTrendColor()}`} data-testid="indicator-stats-trend">
              {getTrendIcon()}
              <span>{trendValue}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}