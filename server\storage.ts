import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { eq, desc, count, sql, and, gte } from "drizzle-orm";
import bcrypt from "bcrypt";
import { 
  players, 
  events, 
  playerStats, 
  steamLinks,
  users,
  type Player,
  type Event,
  type PlayerStats as PlayerStatsType,
  type SteamLink,
  type User,
  type InsertPlayer,
  type InsertEvent,
  type InsertPlayerStats,
  type InsertSteamLink,
  type InsertUser
} from "@shared/schema";

const connectionString = process.env.DATABASE_URL!;
const client = postgres(connectionString);
const db = drizzle(client);

export interface IStorage {
  // User methods (legacy auth)
  getUser(id: string): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean>;
  
  // Player methods
  createOrUpdatePlayer(player: InsertPlayer): Promise<Player>;
  getPlayerBySteamId(steamId: string): Promise<Player | undefined>;
  getActivePlayers(limit?: number): Promise<Player[]>;
  
  // Event methods
  createEvent(event: InsertEvent): Promise<Event>;
  getRecentEvents(limit?: number): Promise<Event[]>;
  getEventsByPlayer(steamId: string, limit?: number): Promise<Event[]>;
  
  // Stats methods
  getPlayerStats(playerId: number, period: "all" | "week" | "month"): Promise<PlayerStatsType | undefined>;
  updatePlayerStats(stats: InsertPlayerStats): Promise<PlayerStatsType>;
  getLeaderboard(metric: "kills" | "deaths" | "revives", period: "all" | "week" | "month", limit?: number): Promise<any[]>;
  
  // Steam linking methods
  createSteamLink(link: InsertSteamLink): Promise<SteamLink>;
  getSteamLink(steamId: string): Promise<SteamLink | undefined>;
}

export class DatabaseStorage implements IStorage {
  // Legacy user methods
  async getUser(id: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);
    return result[0];
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);
    return result[0];
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const saltRounds = 12;
    const hashedPassword = await bcrypt.hash(insertUser.password, saltRounds);
    
    const userWithHashedPassword = {
      ...insertUser,
      password: hashedPassword
    };
    
    const result = await db.insert(users).values(userWithHashedPassword).returning();
    return result[0];
  }

  async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return await bcrypt.compare(plainPassword, hashedPassword);
  }

  // Player methods
  async createOrUpdatePlayer(player: InsertPlayer): Promise<Player> {
    const existing = await this.getPlayerBySteamId(player.steamId64);
    
    if (existing) {
      // Update last seen and display name
      const updated = await db
        .update(players)
        .set({ 
          displayName: player.displayName,
          lastSeenAt: new Date(),
          ...(player.eosId && { eosId: player.eosId })
        })
        .where(eq(players.steamId64, player.steamId64))
        .returning();
      return updated[0];
    } else {
      // Create new player
      const result = await db.insert(players).values(player).returning();
      return result[0];
    }
  }

  async getPlayerBySteamId(steamId: string): Promise<Player | undefined> {
    const result = await db.select().from(players).where(eq(players.steamId64, steamId)).limit(1);
    return result[0];
  }

  async getActivePlayers(limit = 100): Promise<Player[]> {
    return await db
      .select()
      .from(players)
      .orderBy(desc(players.lastSeenAt))
      .limit(limit);
  }

  // Event methods
  async createEvent(event: InsertEvent): Promise<Event> {
    const result = await db.insert(events).values({
      type: event.type,
      serverId: event.serverId,
      actorSteamId: event.actorSteamId, 
      targetSteamId: event.targetSteamId,
      meta: event.meta as any
    }).returning();
    return result[0];
  }

  async getRecentEvents(limit = 50): Promise<Event[]> {
    return await db
      .select()
      .from(events)
      .orderBy(desc(events.timestamp))
      .limit(limit);
  }

  async getEventsByPlayer(steamId: string, limit = 50): Promise<Event[]> {
    return await db
      .select()
      .from(events)
      .where(
        sql`${events.actorSteamId} = ${steamId} OR ${events.targetSteamId} = ${steamId}`
      )
      .orderBy(desc(events.timestamp))
      .limit(limit);
  }

  // Stats methods
  async getPlayerStats(playerId: number, period: "all" | "week" | "month"): Promise<PlayerStatsType | undefined> {
    const result = await db
      .select()
      .from(playerStats)
      .where(and(
        eq(playerStats.playerId, playerId),
        eq(playerStats.periodType, period)
      ))
      .limit(1);
    return result[0];
  }

  async updatePlayerStats(stats: InsertPlayerStats): Promise<PlayerStatsType> {
    const existing = await this.getPlayerStats(stats.playerId, stats.periodType);
    
    // Calculate proper 'since' date based on period type
    let since: Date;
    const now = new Date();
    
    switch (stats.periodType) {
      case "week":
        since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        since = new Date(0); // All time
    }
    
    const statsWithSince = {
      ...stats,
      since
    };
    
    if (existing) {
      const updated = await db
        .update(playerStats)
        .set({
          kills: statsWithSince.kills,
          deaths: statsWithSince.deaths,
          revives: statsWithSince.revives,
          revived: statsWithSince.revived,
          since: statsWithSince.since,
          updatedAt: new Date()
        })
        .where(and(
          eq(playerStats.playerId, statsWithSince.playerId),
          eq(playerStats.periodType, statsWithSince.periodType)
        ))
        .returning();
      return updated[0];
    } else {
      const result = await db.insert(playerStats).values(statsWithSince).returning();
      return result[0];
    }
  }

  async getLeaderboard(metric: "kills" | "deaths" | "revives", period: "all" | "week" | "month", limit = 10): Promise<any[]> {
    // Calculate date threshold for period
    let since: Date;
    const now = new Date();
    
    switch (period) {
      case "week":
        since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "month":
        since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        since = new Date(0); // All time
    }

    // Query events directly for real-time leaderboards
    let query;
    
    if (metric === "kills") {
      query = db
        .select({
          steamId: events.actorSteamId,
          value: count(events.id),
          player: {
            displayName: players.displayName,
            steamId64: players.steamId64
          }
        })
        .from(events)
        .leftJoin(players, eq(events.actorSteamId, players.steamId64))
        .where(and(
          eq(events.type, "kill"),
          gte(events.timestamp, since)
        ))
        .groupBy(events.actorSteamId, players.displayName, players.steamId64)
        .orderBy(desc(count(events.id)))
        .limit(limit);
    } else if (metric === "revives") {
      query = db
        .select({
          steamId: events.actorSteamId,
          value: count(events.id),
          player: {
            displayName: players.displayName,
            steamId64: players.steamId64
          }
        })
        .from(events)
        .leftJoin(players, eq(events.actorSteamId, players.steamId64))
        .where(and(
          eq(events.type, "revive"),
          gte(events.timestamp, since)
        ))
        .groupBy(events.actorSteamId, players.displayName, players.steamId64)
        .orderBy(desc(count(events.id)))
        .limit(limit);
    } else {
      // Deaths
      query = db
        .select({
          steamId: events.targetSteamId,
          value: count(events.id),
          player: {
            displayName: players.displayName,
            steamId64: players.steamId64
          }
        })
        .from(events)
        .leftJoin(players, eq(events.targetSteamId, players.steamId64))
        .where(and(
          eq(events.type, "kill"),
          gte(events.timestamp, since)
        ))
        .groupBy(events.targetSteamId, players.displayName, players.steamId64)
        .orderBy(desc(count(events.id)))
        .limit(limit);
    }
    
    return await query;
  }

  // Steam linking methods
  async createSteamLink(link: InsertSteamLink): Promise<SteamLink> {
    const result = await db.insert(steamLinks).values(link).returning();
    return result[0];
  }

  async getSteamLink(steamId: string): Promise<SteamLink | undefined> {
    const result = await db.select().from(steamLinks).where(eq(steamLinks.steamId64, steamId)).limit(1);
    return result[0];
  }
}

export const storage = new DatabaseStorage();
