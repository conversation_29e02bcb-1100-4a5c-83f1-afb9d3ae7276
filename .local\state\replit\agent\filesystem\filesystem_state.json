{"file_contents": {"design_guidelines.md": {"content": "# Design Guidelines for SquadJS Plugin Dashboard\n\n## Design Approach\n**System-Based Approach**: Using a modified **Carbon Design System** approach, optimized for data-heavy gaming applications with real-time monitoring capabilities. This dashboard prioritizes functionality and information density while maintaining a modern gaming aesthetic.\n\n## Core Design Principles\n- **Information Density**: Maximize data visibility without overwhelming users\n- **Real-time Focus**: Design for live updates and streaming data\n- **Gaming Context**: Reflect the tactical, competitive nature of Squad gameplay\n- **Operational Efficiency**: Enable quick decision-making for server administrators\n\n## Color Palette\n\n### Dark Mode (Primary)\n- **Background**: 220 15% 8% (primary background)\n- **Surface**: 220 12% 12% (cards, panels)\n- **Surface Elevated**: 220 10% 16% (modals, dropdowns)\n- **Primary**: 200 100% 60% (accent actions, highlights)\n- **Success**: 120 60% 50% (positive stats, online status)\n- **Warning**: 45 100% 60% (caution indicators)\n- **Danger**: 0 70% 55% (kills, critical alerts)\n- **Text Primary**: 220 15% 95%\n- **Text Secondary**: 220 10% 70%\n\n### Light Mode (Secondary)\n- **Background**: 220 20% 97%\n- **Surface**: 220 15% 100%\n- **Primary**: 200 90% 45%\n- **Text Primary**: 220 15% 15%\n\n## Typography\n- **Primary Font**: Inter (Google Fonts) - excellent for data tables and UI\n- **Monospace Font**: JetBrains Mono (Google Fonts) - for player IDs, timestamps, and technical data\n- **Scale**: text-xs, text-sm, text-base, text-lg, text-xl for clear hierarchy\n\n## Layout System\n**Tailwind Spacing Units**: Consistent use of 2, 4, 6, 8, 12, 16 units\n- Tight spacing: p-2, m-2 for compact data displays\n- Standard spacing: p-4, gap-4 for general layout\n- Section spacing: p-6, mb-8 for major content areas\n- Page spacing: p-8, gap-12 for top-level organization\n\n## Component Library\n\n### Navigation\n- **Sidebar Navigation**: Fixed left sidebar with server status indicator\n- **Top Bar**: Breadcrumbs, real-time server info, user account dropdown\n- **Tab Navigation**: For switching between Leaderboards, Events, Players\n\n### Data Display\n- **Stats Cards**: Compact metric displays with trend indicators\n- **Data Tables**: Sortable, filterable tables with pagination for player lists and events\n- **Real-time Feeds**: Live event streams with auto-scroll and filtering\n- **Leaderboards**: Ranked lists with player avatars and stats\n\n### Forms & Controls\n- **Filter Panels**: Collapsible sidebar filters for time ranges and event types\n- **Search Bars**: Instant search for players and events\n- **Date Pickers**: For historical data analysis\n- **Toggle Switches**: For real-time updates and notification preferences\n\n### Status Indicators\n- **Server Health**: Color-coded status badges (online/offline/high ping)\n- **Player Status**: Online/offline indicators with last seen timestamps\n- **Connection Quality**: Real-time latency and connection indicators\n\n### Gaming-Specific Elements\n- **Kill Feed**: Styled like in-game kill notifications\n- **Player Cards**: Steam profile integration with stats overlay\n- **Map Indicators**: Server map rotation display\n- **Squad Formation**: Visual representation of team compositions\n\n## Images\nNo large hero images needed. Focus on:\n- **Player Avatars**: Steam profile pictures in circular format\n- **Map Thumbnails**: Small preview images for current server maps\n- **Team/Squad Icons**: Small iconography for team identification\n- **Status Icons**: System health and connection indicators\n\n## Responsive Design\n- **Desktop First**: Primary focus on admin dashboard experience\n- **Tablet Support**: Condensed sidebar, stacked stats cards\n- **Mobile Minimal**: Essential monitoring only, simplified navigation\n\n## Real-time Features\n- **Live Indicators**: Pulsing dots for active connections\n- **Update Animations**: Subtle highlight flashes for new data\n- **Connection Status**: Always-visible WebSocket connection indicator\n- **Auto-refresh**: Configurable refresh rates for different data types\n\nThis design emphasizes operational efficiency and data clarity while maintaining the tactical, professional aesthetic appropriate for gaming server administration.", "size_bytes": 4204}, "drizzle.config.ts": {"content": "import { defineConfig } from \"drizzle-kit\";\n\nif (!process.env.DATABASE_URL) {\n  throw new Error(\"DATABASE_URL, ensure the database is provisioned\");\n}\n\nexport default defineConfig({\n  out: \"./migrations\",\n  schema: \"./shared/schema.ts\",\n  dialect: \"postgresql\",\n  dbCredentials: {\n    url: process.env.DATABASE_URL,\n  },\n});\n", "size_bytes": 325}, "postcss.config.js": {"content": "export default {\n  plugins: {\n    tailwindcss: {},\n    autoprefixer: {},\n  },\n}\n", "size_bytes": 80}, "tailwind.config.ts": {"content": "import type { Config } from \"tailwindcss\";\n\nexport default {\n  darkMode: [\"class\"],\n  content: [\"./client/index.html\", \"./client/src/**/*.{js,jsx,ts,tsx}\"],\n  theme: {\n    extend: {\n      borderRadius: {\n        lg: \".5625rem\", /* 9px */\n        md: \".375rem\", /* 6px */\n        sm: \".1875rem\", /* 3px */\n      },\n      colors: {\n        // Flat / base colors (regular buttons)\n        background: \"hsl(var(--background) / <alpha-value>)\",\n        foreground: \"hsl(var(--foreground) / <alpha-value>)\",\n        border: \"hsl(var(--border) / <alpha-value>)\",\n        input: \"hsl(var(--input) / <alpha-value>)\",\n        card: {\n          DEFAULT: \"hsl(var(--card) / <alpha-value>)\",\n          foreground: \"hsl(var(--card-foreground) / <alpha-value>)\",\n          border: \"hsl(var(--card-border) / <alpha-value>)\",\n        },\n        popover: {\n          DEFAULT: \"hsl(var(--popover) / <alpha-value>)\",\n          foreground: \"hsl(var(--popover-foreground) / <alpha-value>)\",\n          border: \"hsl(var(--popover-border) / <alpha-value>)\",\n        },\n        primary: {\n          DEFAULT: \"hsl(var(--primary) / <alpha-value>)\",\n          foreground: \"hsl(var(--primary-foreground) / <alpha-value>)\",\n          border: \"var(--primary-border)\",\n        },\n        secondary: {\n          DEFAULT: \"hsl(var(--secondary) / <alpha-value>)\",\n          foreground: \"hsl(var(--secondary-foreground) / <alpha-value>)\",\n          border: \"var(--secondary-border)\",\n        },\n        muted: {\n          DEFAULT: \"hsl(var(--muted) / <alpha-value>)\",\n          foreground: \"hsl(var(--muted-foreground) / <alpha-value>)\",\n          border: \"var(--muted-border)\",\n        },\n        accent: {\n          DEFAULT: \"hsl(var(--accent) / <alpha-value>)\",\n          foreground: \"hsl(var(--accent-foreground) / <alpha-value>)\",\n          border: \"var(--accent-border)\",\n        },\n        destructive: {\n          DEFAULT: \"hsl(var(--destructive) / <alpha-value>)\",\n          foreground: \"hsl(var(--destructive-foreground) / <alpha-value>)\",\n          border: \"var(--destructive-border)\",\n        },\n        ring: \"hsl(var(--ring) / <alpha-value>)\",\n        chart: {\n          \"1\": \"hsl(var(--chart-1) / <alpha-value>)\",\n          \"2\": \"hsl(var(--chart-2) / <alpha-value>)\",\n          \"3\": \"hsl(var(--chart-3) / <alpha-value>)\",\n          \"4\": \"hsl(var(--chart-4) / <alpha-value>)\",\n          \"5\": \"hsl(var(--chart-5) / <alpha-value>)\",\n        },\n        sidebar: {\n          ring: \"hsl(var(--sidebar-ring) / <alpha-value>)\",\n          DEFAULT: \"hsl(var(--sidebar) / <alpha-value>)\",\n          foreground: \"hsl(var(--sidebar-foreground) / <alpha-value>)\",\n          border: \"hsl(var(--sidebar-border) / <alpha-value>)\",\n        },\n        \"sidebar-primary\": {\n          DEFAULT: \"hsl(var(--sidebar-primary) / <alpha-value>)\",\n          foreground: \"hsl(var(--sidebar-primary-foreground) / <alpha-value>)\",\n          border: \"var(--sidebar-primary-border)\",\n        },\n        \"sidebar-accent\": {\n          DEFAULT: \"hsl(var(--sidebar-accent) / <alpha-value>)\",\n          foreground: \"hsl(var(--sidebar-accent-foreground) / <alpha-value>)\",\n          border: \"var(--sidebar-accent-border)\"\n        },\n        status: {\n          online: \"rgb(34 197 94)\",\n          away: \"rgb(245 158 11)\",\n          busy: \"rgb(239 68 68)\",\n          offline: \"rgb(156 163 175)\",\n        },\n      },\n      fontFamily: {\n        sans: [\"var(--font-sans)\"],\n        serif: [\"var(--font-serif)\"],\n        mono: [\"var(--font-mono)\"],\n      },\n      keyframes: {\n        \"accordion-down\": {\n          from: { height: \"0\" },\n          to: { height: \"var(--radix-accordion-content-height)\" },\n        },\n        \"accordion-up\": {\n          from: { height: \"var(--radix-accordion-content-height)\" },\n          to: { height: \"0\" },\n        },\n      },\n      animation: {\n        \"accordion-down\": \"accordion-down 0.2s ease-out\",\n        \"accordion-up\": \"accordion-up 0.2s ease-out\",\n      },\n    },\n  },\n  plugins: [require(\"tailwindcss-animate\"), require(\"@tailwindcss/typography\")],\n} satisfies Config;\n", "size_bytes": 4050}, "vite.config.ts": {"content": "import { defineConfig } from \"vite\";\nimport react from \"@vitejs/plugin-react\";\nimport path from \"path\";\nimport runtimeErrorOverlay from \"@replit/vite-plugin-runtime-error-modal\";\n\nexport default defineConfig({\n  plugins: [\n    react(),\n    runtimeErrorOverlay(),\n    ...(process.env.NODE_ENV !== \"production\" &&\n    process.env.REPL_ID !== undefined\n      ? [\n          await import(\"@replit/vite-plugin-cartographer\").then((m) =>\n            m.cartographer(),\n          ),\n        ]\n      : []),\n  ],\n  resolve: {\n    alias: {\n      \"@\": path.resolve(import.meta.dirname, \"client\", \"src\"),\n      \"@shared\": path.resolve(import.meta.dirname, \"shared\"),\n      \"@assets\": path.resolve(import.meta.dirname, \"attached_assets\"),\n    },\n  },\n  root: path.resolve(import.meta.dirname, \"client\"),\n  build: {\n    outDir: path.resolve(import.meta.dirname, \"dist/public\"),\n    emptyOutDir: true,\n  },\n  server: {\n    fs: {\n      strict: true,\n      deny: [\"**/.*\"],\n    },\n  },\n});\n", "size_bytes": 971}, "server/index.ts": {"content": "import express, { type Request, Response, NextFunction } from \"express\";\nimport { registerRoutes } from \"./routes\";\nimport { setupVite, serveStatic, log } from \"./vite\";\n\nconst app = express();\napp.use(express.json());\napp.use(express.urlencoded({ extended: false }));\n\napp.use((req, res, next) => {\n  const start = Date.now();\n  const path = req.path;\n  let capturedJsonResponse: Record<string, any> | undefined = undefined;\n\n  const originalResJson = res.json;\n  res.json = function (bodyJson, ...args) {\n    capturedJsonResponse = bodyJson;\n    return originalResJson.apply(res, [bodyJson, ...args]);\n  };\n\n  res.on(\"finish\", () => {\n    const duration = Date.now() - start;\n    if (path.startsWith(\"/api\")) {\n      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;\n      if (capturedJsonResponse) {\n        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;\n      }\n\n      if (logLine.length > 80) {\n        logLine = logLine.slice(0, 79) + \"…\";\n      }\n\n      log(logLine);\n    }\n  });\n\n  next();\n});\n\n(async () => {\n  const server = await registerRoutes(app);\n\n  app.use((err: any, _req: Request, res: Response, _next: NextFunction) => {\n    const status = err.status || err.statusCode || 500;\n    const message = err.message || \"Internal Server Error\";\n\n    res.status(status).json({ message });\n    throw err;\n  });\n\n  // importantly only setup vite in development and after\n  // setting up all the other routes so the catch-all route\n  // doesn't interfere with the other routes\n  if (app.get(\"env\") === \"development\") {\n    await setupVite(app, server);\n  } else {\n    serveStatic(app);\n  }\n\n  // ALWAYS serve the app on the port specified in the environment variable PORT\n  // Other ports are firewalled. Default to 5000 if not specified.\n  // this serves both the API and the client.\n  // It is the only port that is not firewalled.\n  const port = parseInt(process.env.PORT || '5000', 10);\n  server.listen({\n    port,\n    host: \"0.0.0.0\",\n    reusePort: true,\n  }, () => {\n    log(`serving on port ${port}`);\n  });\n})();\n", "size_bytes": 2066}, "server/routes.ts": {"content": "import type { Express } from \"express\";\nimport { createServer, type Server } from \"http\";\nimport { Server as SocketIOServer } from \"socket.io\";\nimport { storage } from \"./storage\";\nimport { insertEventSchema, insertPlayerSchema } from \"@shared/schema\";\nimport { z } from \"zod\";\n\nexport async function registerRoutes(app: Express): Promise<Server> {\n  const httpServer = createServer(app);\n  \n  // Initialize Socket.IO for real-time updates\n  const io = new SocketIOServer(httpServer, {\n    cors: {\n      origin: \"*\",\n      methods: [\"GET\", \"POST\"]\n    }\n  });\n\n  // WebSocket connection handling\n  io.on(\"connection\", (socket) => {\n    console.log(\"Client connected:\", socket.id);\n    \n    socket.on(\"disconnect\", () => {\n      console.log(\"Client disconnected:\", socket.id);\n    });\n  });\n\n  // Helper function to broadcast updates to all connected clients\n  const broadcastUpdate = (event: string, data: any) => {\n    io.emit(event, data);\n  };\n\n  // GET /api/server - Server information including player count and current map\n  app.get(\"/api/server\", async (req, res) => {\n    try {\n      const activePlayers = await storage.getActivePlayers();\n      const recentEvents = await storage.getRecentEvents(10);\n      \n      // Get latest map info from recent events\n      const mapEvent = recentEvents.find(e => e.meta && 'map' in e.meta);\n      const currentMap = mapEvent?.meta?.map || \"Unknown Map\";\n      \n      const serverInfo = {\n        name: \"Squad Server\",\n        currentMap,\n        playerCount: activePlayers.length,\n        maxPlayers: 96,\n        status: \"online\" as const,\n        lastUpdate: new Date().toISOString(),\n        uptime: \"98.5%\"\n      };\n      \n      res.json(serverInfo);\n    } catch (error) {\n      console.error(\"Error fetching server info:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // GET /api/players - List of active players with basic stats\n  app.get(\"/api/players\", async (req, res) => {\n    try {\n      const limit = parseInt(req.query.limit as string) || 100;\n      const search = req.query.search as string;\n      \n      let players = await storage.getActivePlayers(limit);\n      \n      // Filter by search term if provided\n      if (search) {\n        const searchLower = search.toLowerCase();\n        players = players.filter(p => \n          p.displayName.toLowerCase().includes(searchLower) ||\n          p.steamId64.includes(search)\n        );\n      }\n      \n      // Get basic stats for each player\n      const playersWithStats = await Promise.all(\n        players.map(async (player) => {\n          const stats = await storage.getPlayerStats(player.id, \"all\");\n          return {\n            steamId: player.steamId64,\n            name: player.displayName,\n            eosId: player.eosId,\n            kills: stats?.kills || 0,\n            deaths: stats?.deaths || 0,\n            revives: stats?.revives || 0,\n            status: isPlayerOnline(player.lastSeenAt) ? \"online\" : \"offline\",\n            lastSeen: player.lastSeenAt,\n            squad: \"Unknown\", // This would come from live game data\n            role: \"Unknown\"   // This would come from live game data\n          };\n        })\n      );\n      \n      res.json(playersWithStats);\n    } catch (error) {\n      console.error(\"Error fetching players:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // GET /api/player/:steamId - Individual player detailed stats\n  app.get(\"/api/player/:steamId\", async (req, res) => {\n    try {\n      const { steamId } = req.params;\n      const player = await storage.getPlayerBySteamId(steamId);\n      \n      if (!player) {\n        return res.status(404).json({ error: \"Player not found\" });\n      }\n      \n      // Get stats for different periods\n      const [allTimeStats, weekStats, monthStats] = await Promise.all([\n        storage.getPlayerStats(player.id, \"all\"),\n        storage.getPlayerStats(player.id, \"week\"),\n        storage.getPlayerStats(player.id, \"month\")\n      ]);\n      \n      // Get recent events for this player\n      const recentEvents = await storage.getEventsByPlayer(steamId, 20);\n      \n      const playerDetails = {\n        steamId: player.steamId64,\n        eosId: player.eosId,\n        name: player.displayName,\n        firstSeen: player.firstSeenAt,\n        lastSeen: player.lastSeenAt,\n        status: isPlayerOnline(player.lastSeenAt) ? \"online\" : \"offline\",\n        stats: {\n          allTime: allTimeStats || { kills: 0, deaths: 0, revives: 0, revived: 0 },\n          week: weekStats || { kills: 0, deaths: 0, revives: 0, revived: 0 },\n          month: monthStats || { kills: 0, deaths: 0, revives: 0, revived: 0 }\n        },\n        recentEvents: recentEvents.map(event => ({\n          id: event.id,\n          type: event.type,\n          timestamp: event.timestamp,\n          meta: event.meta\n        }))\n      };\n      \n      res.json(playerDetails);\n    } catch (error) {\n      console.error(\"Error fetching player details:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // GET /api/leaderboard/:metric/:period - Leaderboards for kills/deaths/revives\n  app.get(\"/api/leaderboard/:metric/:period\", async (req, res) => {\n    try {\n      const { metric, period } = req.params;\n      const limit = parseInt(req.query.limit as string) || 10;\n      \n      // Validate parameters\n      if (![\"kills\", \"deaths\", \"revives\"].includes(metric)) {\n        return res.status(400).json({ error: \"Invalid metric. Must be kills, deaths, or revives\" });\n      }\n      \n      if (![\"all\", \"week\", \"month\"].includes(period)) {\n        return res.status(400).json({ error: \"Invalid period. Must be all, week, or month\" });\n      }\n      \n      const leaderboard = await storage.getLeaderboard(\n        metric as \"kills\" | \"deaths\" | \"revives\",\n        period as \"all\" | \"week\" | \"month\",\n        limit\n      );\n      \n      const formattedLeaderboard = leaderboard.map((entry, index) => ({\n        rank: index + 1,\n        steamId: entry.steamId,\n        name: entry.player?.displayName || \"Unknown Player\",\n        value: parseInt(entry.value) || 0\n      }));\n      \n      res.json({\n        metric,\n        period,\n        entries: formattedLeaderboard,\n        lastUpdated: new Date().toISOString()\n      });\n    } catch (error) {\n      console.error(\"Error fetching leaderboard:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // GET /api/events - Recent game events with filtering\n  app.get(\"/api/events\", async (req, res) => {\n    try {\n      const limit = parseInt(req.query.limit as string) || 50;\n      const type = req.query.type as string;\n      const steamId = req.query.steamId as string;\n      \n      let events;\n      \n      if (steamId) {\n        events = await storage.getEventsByPlayer(steamId, limit);\n      } else {\n        events = await storage.getRecentEvents(limit);\n      }\n      \n      // Filter by event type if specified\n      if (type) {\n        events = events.filter(e => e.type === type);\n      }\n      \n      const formattedEvents = events.map(event => ({\n        id: event.id,\n        type: event.type,\n        timestamp: event.timestamp,\n        serverId: event.serverId,\n        actor: event.actorSteamId,\n        target: event.targetSteamId,\n        meta: event.meta\n      }));\n      \n      res.json(formattedEvents);\n    } catch (error) {\n      console.error(\"Error fetching events:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // POST /api/events - Endpoint for SquadJS to send game events\n  app.post(\"/api/events\", async (req, res) => {\n    try {\n      const eventData = insertEventSchema.parse(req.body);\n      \n      // Create the event\n      const event = await storage.createEvent(eventData);\n      \n      // Broadcast the event to all connected clients\n      broadcastUpdate(\"newEvent\", {\n        id: event.id,\n        type: event.type,\n        timestamp: event.timestamp,\n        actor: event.actorSteamId,\n        target: event.targetSteamId,\n        meta: event.meta\n      });\n      \n      // If this is a player join/leave event, update player data\n      if (eventData.type === \"join\" && eventData.actorSteamId) {\n        const playerData = req.body.playerData;\n        if (playerData) {\n          await storage.createOrUpdatePlayer({\n            steamId64: eventData.actorSteamId,\n            displayName: playerData.displayName || `Player_${eventData.actorSteamId.slice(-6)}`,\n            eosId: playerData.eosId\n          });\n        }\n      }\n      \n      res.status(201).json({ success: true, event });\n    } catch (error) {\n      if (error instanceof z.ZodError) {\n        return res.status(400).json({ error: \"Invalid event data\", details: error.errors });\n      }\n      console.error(\"Error creating event:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // POST /api/players - Endpoint for SquadJS to update player information\n  app.post(\"/api/players\", async (req, res) => {\n    try {\n      const playerData = insertPlayerSchema.parse(req.body);\n      \n      const player = await storage.createOrUpdatePlayer(playerData);\n      \n      // Broadcast player update\n      broadcastUpdate(\"playerUpdate\", {\n        steamId: player.steamId64,\n        name: player.displayName,\n        status: \"online\",\n        lastSeen: player.lastSeenAt\n      });\n      \n      res.status(201).json({ success: true, player });\n    } catch (error) {\n      if (error instanceof z.ZodError) {\n        return res.status(400).json({ error: \"Invalid player data\", details: error.errors });\n      }\n      console.error(\"Error updating player:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // POST /api/stats/update - Endpoint for updating player statistics\n  app.post(\"/api/stats/update\", async (req, res) => {\n    try {\n      const { steamId, period, stats } = req.body;\n      \n      if (!steamId || !period || !stats) {\n        return res.status(400).json({ error: \"Missing required fields: steamId, period, stats\" });\n      }\n      \n      const player = await storage.getPlayerBySteamId(steamId);\n      if (!player) {\n        return res.status(404).json({ error: \"Player not found\" });\n      }\n      \n      // Calculate proper 'since' date based on period type\n      let since: Date;\n      const now = new Date();\n      \n      switch (period) {\n        case \"week\":\n          since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n          break;\n        case \"month\":\n          since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n          break;\n        default:\n          since = new Date(0); // All time\n      }\n\n      const updatedStats = await storage.updatePlayerStats({\n        playerId: player.id,\n        periodType: period,\n        since,\n        kills: stats.kills || 0,\n        deaths: stats.deaths || 0,\n        revives: stats.revives || 0,\n        revived: stats.revived || 0\n      });\n      \n      // Broadcast stats update\n      broadcastUpdate(\"statsUpdate\", {\n        steamId,\n        period,\n        stats: updatedStats\n      });\n      \n      res.json({ success: true, stats: updatedStats });\n    } catch (error) {\n      console.error(\"Error updating stats:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // GET /api/dashboard/summary - Summary data for dashboard overview\n  app.get(\"/api/dashboard/summary\", async (req, res) => {\n    try {\n      const [activePlayers, recentEvents, killsLeaderboard, revivesLeaderboard] = await Promise.all([\n        storage.getActivePlayers(100),\n        storage.getRecentEvents(100),\n        storage.getLeaderboard(\"kills\", \"week\", 5),\n        storage.getLeaderboard(\"revives\", \"week\", 5)\n      ]);\n      \n      // Calculate summary stats\n      const onlinePlayers = activePlayers.filter(p => isPlayerOnline(p.lastSeenAt));\n      const last24Hours = new Date(Date.now() - 24 * 60 * 60 * 1000);\n      const recent24hEvents = recentEvents.filter(e => e.timestamp >= last24Hours);\n      \n      const totalKills = recent24hEvents.filter(e => e.type === \"kill\").length;\n      const totalRevives = recent24hEvents.filter(e => e.type === \"revive\").length;\n      \n      const summary = {\n        activePlayers: onlinePlayers.length,\n        maxPlayers: 96,\n        totalKills24h: totalKills,\n        totalRevives24h: totalRevives,\n        serverUptime: \"98.5%\",\n        topKillers: killsLeaderboard.slice(0, 5).map((entry, index) => ({\n          rank: index + 1,\n          name: entry.player?.displayName || \"Unknown\",\n          value: parseInt(entry.value) || 0\n        })),\n        topMedics: revivesLeaderboard.slice(0, 5).map((entry, index) => ({\n          rank: index + 1,\n          name: entry.player?.displayName || \"Unknown\",\n          value: parseInt(entry.value) || 0\n        })),\n        recentEvents: recent24hEvents.slice(0, 10).map(event => ({\n          id: event.id,\n          type: event.type,\n          timestamp: event.timestamp,\n          actor: event.actorSteamId,\n          target: event.targetSteamId,\n          meta: event.meta\n        }))\n      };\n      \n      res.json(summary);\n    } catch (error) {\n      console.error(\"Error fetching dashboard summary:\", error);\n      res.status(500).json({ error: \"Internal server error\" });\n    }\n  });\n\n  // Health check endpoint\n  app.get(\"/api/health\", (req, res) => {\n    res.json({ \n      status: \"healthy\", \n      timestamp: new Date().toISOString(),\n      uptime: process.uptime()\n    });\n  });\n\n  // Development endpoint to populate sample data\n  app.post(\"/api/dev/populate-sample-data\", async (req, res) => {\n    try {\n      // Create sample players\n      const samplePlayers = [\n        {\n          steamId64: \"76561199585954249\",\n          displayName: \"KING\",\n          eosId: \"eos_123456789\"\n        },\n        {\n          steamId64: \"76561198123456789\",\n          displayName: \"GoldenEagle\",\n          eosId: \"eos_987654321\"\n        },\n        {\n          steamId64: \"76561198987654321\",\n          displayName: \"SniperElite\",\n          eosId: \"eos_555666777\"\n        },\n        {\n          steamId64: \"76561198111222333\",\n          displayName: \"WarriorX\",\n          eosId: \"eos_111222333\"\n        },\n        {\n          steamId64: \"76561198444555666\",\n          displayName: \"MedicsRule\",\n          eosId: \"eos_444555666\"\n        },\n        {\n          steamId64: \"76561198777888999\",\n          displayName: \"LifeSaver\",\n          eosId: \"eos_777888999\"\n        }\n      ];\n\n      // Insert players\n      const createdPlayers = [];\n      for (const player of samplePlayers) {\n        const created = await storage.createOrUpdatePlayer(player);\n        createdPlayers.push(created);\n      }\n\n      // Create sample events\n      const sampleEvents = [\n        {\n          type: \"join\" as const,\n          serverId: 1,\n          actorSteamId: \"76561199585954249\",\n          meta: { map: \"GE AlBasrah Invasion v7\" }\n        },\n        {\n          type: \"join\" as const,\n          serverId: 1,\n          actorSteamId: \"76561198123456789\",\n          meta: { map: \"GE AlBasrah Invasion v7\" }\n        },\n        {\n          type: \"kill\" as const,\n          serverId: 1,\n          actorSteamId: \"76561199585954249\",\n          targetSteamId: \"76561198123456789\",\n          meta: { weapon: \"M4 SOPMOD\", headshot: true }\n        },\n        {\n          type: \"revive\" as const,\n          serverId: 1,\n          actorSteamId: \"76561198444555666\",\n          targetSteamId: \"76561198123456789\",\n          meta: {}\n        },\n        {\n          type: \"kill\" as const,\n          serverId: 1,\n          actorSteamId: \"76561198987654321\",\n          targetSteamId: \"76561198111222333\",\n          meta: { weapon: \"SVD\", headshot: true }\n        }\n      ];\n\n      // Insert events\n      const createdEvents = [];\n      for (const event of sampleEvents) {\n        const created = await storage.createEvent(event);\n        createdEvents.push(created);\n      }\n\n      // Create sample stats\n      for (const player of createdPlayers) {\n        // All-time stats\n        await storage.updatePlayerStats({\n          playerId: player.id,\n          periodType: \"all\",\n          since: new Date(0), // All time\n          kills: Math.floor(Math.random() * 50) + 10,\n          deaths: Math.floor(Math.random() * 30) + 5,\n          revives: Math.floor(Math.random() * 40) + 5,\n          revived: Math.floor(Math.random() * 20) + 2\n        });\n\n        // Weekly stats\n        const weekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);\n        await storage.updatePlayerStats({\n          playerId: player.id,\n          periodType: \"week\",\n          since: weekAgo,\n          kills: Math.floor(Math.random() * 20) + 5,\n          deaths: Math.floor(Math.random() * 15) + 2,\n          revives: Math.floor(Math.random() * 15) + 2,\n          revived: Math.floor(Math.random() * 8) + 1\n        });\n\n        // Monthly stats\n        const monthAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);\n        await storage.updatePlayerStats({\n          playerId: player.id,\n          periodType: \"month\",\n          since: monthAgo,\n          kills: Math.floor(Math.random() * 35) + 8,\n          deaths: Math.floor(Math.random() * 25) + 3,\n          revives: Math.floor(Math.random() * 25) + 3,\n          revived: Math.floor(Math.random() * 12) + 1\n        });\n      }\n\n      res.json({\n        success: true,\n        message: \"Sample data populated successfully\",\n        data: {\n          players: createdPlayers.length,\n          events: createdEvents.length,\n          statsEntries: createdPlayers.length * 3 // 3 periods per player\n        }\n      });\n    } catch (error) {\n      console.error(\"Error populating sample data:\", error);\n      res.status(500).json({ error: \"Failed to populate sample data\" });\n    }\n  });\n\n  // Debug endpoint to check raw storage data\n  app.get(\"/api/debug/events\", async (req, res) => {\n    try {\n      const events = await storage.getRecentEvents(5);\n      console.log(\"Raw storage events:\", JSON.stringify(events, null, 2));\n      res.json({\n        count: events.length,\n        rawEvents: events,\n        firstEvent: events[0] || null\n      });\n    } catch (error) {\n      console.error(\"Debug events error:\", error);\n      res.status(500).json({ error: error.message });\n    }\n  });\n\n  return httpServer;\n}\n\n// Helper function to determine if a player is considered online\nfunction isPlayerOnline(lastSeenAt: Date): boolean {\n  const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);\n  return lastSeenAt > fiveMinutesAgo;\n}\n\n// Export the io instance for use in other parts of the application\nexport let socketIO: SocketIOServer;\n\nexport function setSocketIO(io: SocketIOServer) {\n  socketIO = io;\n}\n", "size_bytes": 18772}, "server/storage.ts": {"content": "import { drizzle } from \"drizzle-orm/postgres-js\";\nimport postgres from \"postgres\";\nimport { eq, desc, count, sql, and, gte } from \"drizzle-orm\";\nimport bcrypt from \"bcrypt\";\nimport { \n  players, \n  events, \n  playerStats, \n  steamLinks,\n  users,\n  type Player,\n  type Event,\n  type PlayerStats as PlayerStatsType,\n  type SteamLink,\n  type User,\n  type InsertPlayer,\n  type InsertEvent,\n  type InsertPlayerStats,\n  type InsertSteamLink,\n  type InsertUser\n} from \"@shared/schema\";\n\nconst connectionString = process.env.DATABASE_URL!;\nconst client = postgres(connectionString);\nconst db = drizzle(client);\n\nexport interface IStorage {\n  // User methods (legacy auth)\n  getUser(id: string): Promise<User | undefined>;\n  getUserByUsername(username: string): Promise<User | undefined>;\n  createUser(user: InsertUser): Promise<User>;\n  verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean>;\n  \n  // Player methods\n  createOrUpdatePlayer(player: InsertPlayer): Promise<Player>;\n  getPlayerBySteamId(steamId: string): Promise<Player | undefined>;\n  getActivePlayers(limit?: number): Promise<Player[]>;\n  \n  // Event methods\n  createEvent(event: InsertEvent): Promise<Event>;\n  getRecentEvents(limit?: number): Promise<Event[]>;\n  getEventsByPlayer(steamId: string, limit?: number): Promise<Event[]>;\n  \n  // Stats methods\n  getPlayerStats(playerId: number, period: \"all\" | \"week\" | \"month\"): Promise<PlayerStatsType | undefined>;\n  updatePlayerStats(stats: InsertPlayerStats): Promise<PlayerStatsType>;\n  getLeaderboard(metric: \"kills\" | \"deaths\" | \"revives\", period: \"all\" | \"week\" | \"month\", limit?: number): Promise<any[]>;\n  \n  // Steam linking methods\n  createSteamLink(link: InsertSteamLink): Promise<SteamLink>;\n  getSteamLink(steamId: string): Promise<SteamLink | undefined>;\n}\n\nexport class DatabaseStorage implements IStorage {\n  // Legacy user methods\n  async getUser(id: string): Promise<User | undefined> {\n    const result = await db.select().from(users).where(eq(users.id, id)).limit(1);\n    return result[0];\n  }\n\n  async getUserByUsername(username: string): Promise<User | undefined> {\n    const result = await db.select().from(users).where(eq(users.username, username)).limit(1);\n    return result[0];\n  }\n\n  async createUser(insertUser: InsertUser): Promise<User> {\n    const saltRounds = 12;\n    const hashedPassword = await bcrypt.hash(insertUser.password, saltRounds);\n    \n    const userWithHashedPassword = {\n      ...insertUser,\n      password: hashedPassword\n    };\n    \n    const result = await db.insert(users).values(userWithHashedPassword).returning();\n    return result[0];\n  }\n\n  async verifyPassword(plainPassword: string, hashedPassword: string): Promise<boolean> {\n    return await bcrypt.compare(plainPassword, hashedPassword);\n  }\n\n  // Player methods\n  async createOrUpdatePlayer(player: InsertPlayer): Promise<Player> {\n    const existing = await this.getPlayerBySteamId(player.steamId64);\n    \n    if (existing) {\n      // Update last seen and display name\n      const updated = await db\n        .update(players)\n        .set({ \n          displayName: player.displayName,\n          lastSeenAt: new Date(),\n          ...(player.eosId && { eosId: player.eosId })\n        })\n        .where(eq(players.steamId64, player.steamId64))\n        .returning();\n      return updated[0];\n    } else {\n      // Create new player\n      const result = await db.insert(players).values(player).returning();\n      return result[0];\n    }\n  }\n\n  async getPlayerBySteamId(steamId: string): Promise<Player | undefined> {\n    const result = await db.select().from(players).where(eq(players.steamId64, steamId)).limit(1);\n    return result[0];\n  }\n\n  async getActivePlayers(limit = 100): Promise<Player[]> {\n    return await db\n      .select()\n      .from(players)\n      .orderBy(desc(players.lastSeenAt))\n      .limit(limit);\n  }\n\n  // Event methods\n  async createEvent(event: InsertEvent): Promise<Event> {\n    const result = await db.insert(events).values({\n      type: event.type,\n      serverId: event.serverId,\n      actorSteamId: event.actorSteamId, \n      targetSteamId: event.targetSteamId,\n      meta: event.meta as any\n    }).returning();\n    return result[0];\n  }\n\n  async getRecentEvents(limit = 50): Promise<Event[]> {\n    return await db\n      .select()\n      .from(events)\n      .orderBy(desc(events.timestamp))\n      .limit(limit);\n  }\n\n  async getEventsByPlayer(steamId: string, limit = 50): Promise<Event[]> {\n    return await db\n      .select()\n      .from(events)\n      .where(\n        sql`${events.actorSteamId} = ${steamId} OR ${events.targetSteamId} = ${steamId}`\n      )\n      .orderBy(desc(events.timestamp))\n      .limit(limit);\n  }\n\n  // Stats methods\n  async getPlayerStats(playerId: number, period: \"all\" | \"week\" | \"month\"): Promise<PlayerStatsType | undefined> {\n    const result = await db\n      .select()\n      .from(playerStats)\n      .where(and(\n        eq(playerStats.playerId, playerId),\n        eq(playerStats.periodType, period)\n      ))\n      .limit(1);\n    return result[0];\n  }\n\n  async updatePlayerStats(stats: InsertPlayerStats): Promise<PlayerStatsType> {\n    const existing = await this.getPlayerStats(stats.playerId, stats.periodType);\n    \n    // Calculate proper 'since' date based on period type\n    let since: Date;\n    const now = new Date();\n    \n    switch (stats.periodType) {\n      case \"week\":\n        since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case \"month\":\n        since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        break;\n      default:\n        since = new Date(0); // All time\n    }\n    \n    const statsWithSince = {\n      ...stats,\n      since\n    };\n    \n    if (existing) {\n      const updated = await db\n        .update(playerStats)\n        .set({\n          kills: statsWithSince.kills,\n          deaths: statsWithSince.deaths,\n          revives: statsWithSince.revives,\n          revived: statsWithSince.revived,\n          since: statsWithSince.since,\n          updatedAt: new Date()\n        })\n        .where(and(\n          eq(playerStats.playerId, statsWithSince.playerId),\n          eq(playerStats.periodType, statsWithSince.periodType)\n        ))\n        .returning();\n      return updated[0];\n    } else {\n      const result = await db.insert(playerStats).values(statsWithSince).returning();\n      return result[0];\n    }\n  }\n\n  async getLeaderboard(metric: \"kills\" | \"deaths\" | \"revives\", period: \"all\" | \"week\" | \"month\", limit = 10): Promise<any[]> {\n    // Calculate date threshold for period\n    let since: Date;\n    const now = new Date();\n    \n    switch (period) {\n      case \"week\":\n        since = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case \"month\":\n        since = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n        break;\n      default:\n        since = new Date(0); // All time\n    }\n\n    // Query events directly for real-time leaderboards\n    let query;\n    \n    if (metric === \"kills\") {\n      query = db\n        .select({\n          steamId: events.actorSteamId,\n          value: count(events.id),\n          player: {\n            displayName: players.displayName,\n            steamId64: players.steamId64\n          }\n        })\n        .from(events)\n        .leftJoin(players, eq(events.actorSteamId, players.steamId64))\n        .where(and(\n          eq(events.type, \"kill\"),\n          gte(events.timestamp, since)\n        ))\n        .groupBy(events.actorSteamId, players.displayName, players.steamId64)\n        .orderBy(desc(count(events.id)))\n        .limit(limit);\n    } else if (metric === \"revives\") {\n      query = db\n        .select({\n          steamId: events.actorSteamId,\n          value: count(events.id),\n          player: {\n            displayName: players.displayName,\n            steamId64: players.steamId64\n          }\n        })\n        .from(events)\n        .leftJoin(players, eq(events.actorSteamId, players.steamId64))\n        .where(and(\n          eq(events.type, \"revive\"),\n          gte(events.timestamp, since)\n        ))\n        .groupBy(events.actorSteamId, players.displayName, players.steamId64)\n        .orderBy(desc(count(events.id)))\n        .limit(limit);\n    } else {\n      // Deaths\n      query = db\n        .select({\n          steamId: events.targetSteamId,\n          value: count(events.id),\n          player: {\n            displayName: players.displayName,\n            steamId64: players.steamId64\n          }\n        })\n        .from(events)\n        .leftJoin(players, eq(events.targetSteamId, players.steamId64))\n        .where(and(\n          eq(events.type, \"kill\"),\n          gte(events.timestamp, since)\n        ))\n        .groupBy(events.targetSteamId, players.displayName, players.steamId64)\n        .orderBy(desc(count(events.id)))\n        .limit(limit);\n    }\n    \n    return await query;\n  }\n\n  // Steam linking methods\n  async createSteamLink(link: InsertSteamLink): Promise<SteamLink> {\n    const result = await db.insert(steamLinks).values(link).returning();\n    return result[0];\n  }\n\n  async getSteamLink(steamId: string): Promise<SteamLink | undefined> {\n    const result = await db.select().from(steamLinks).where(eq(steamLinks.steamId64, steamId)).limit(1);\n    return result[0];\n  }\n}\n\nexport const storage = new DatabaseStorage();\n", "size_bytes": 9307}, "server/vite.ts": {"content": "import express, { type Express } from \"express\";\nimport fs from \"fs\";\nimport path from \"path\";\nimport { createServer as createViteServer, createLogger } from \"vite\";\nimport { type Server } from \"http\";\nimport viteConfig from \"../vite.config\";\nimport { nanoid } from \"nanoid\";\n\nconst viteLogger = createLogger();\n\nexport function log(message: string, source = \"express\") {\n  const formattedTime = new Date().toLocaleTimeString(\"en-US\", {\n    hour: \"numeric\",\n    minute: \"2-digit\",\n    second: \"2-digit\",\n    hour12: true,\n  });\n\n  console.log(`${formattedTime} [${source}] ${message}`);\n}\n\nexport async function setupVite(app: Express, server: Server) {\n  const serverOptions = {\n    middlewareMode: true,\n    hmr: { server },\n    allowedHosts: true as const,\n  };\n\n  const vite = await createViteServer({\n    ...viteConfig,\n    configFile: false,\n    customLogger: {\n      ...viteLogger,\n      error: (msg, options) => {\n        viteLogger.error(msg, options);\n        process.exit(1);\n      },\n    },\n    server: serverOptions,\n    appType: \"custom\",\n  });\n\n  app.use(vite.middlewares);\n  app.use(\"*\", async (req, res, next) => {\n    const url = req.originalUrl;\n\n    try {\n      const clientTemplate = path.resolve(\n        import.meta.dirname,\n        \"..\",\n        \"client\",\n        \"index.html\",\n      );\n\n      // always reload the index.html file from disk incase it changes\n      let template = await fs.promises.readFile(clientTemplate, \"utf-8\");\n      template = template.replace(\n        `src=\"/src/main.tsx\"`,\n        `src=\"/src/main.tsx?v=${nanoid()}\"`,\n      );\n      const page = await vite.transformIndexHtml(url, template);\n      res.status(200).set({ \"Content-Type\": \"text/html\" }).end(page);\n    } catch (e) {\n      vite.ssrFixStacktrace(e as Error);\n      next(e);\n    }\n  });\n}\n\nexport function serveStatic(app: Express) {\n  const distPath = path.resolve(import.meta.dirname, \"public\");\n\n  if (!fs.existsSync(distPath)) {\n    throw new Error(\n      `Could not find the build directory: ${distPath}, make sure to build the client first`,\n    );\n  }\n\n  app.use(express.static(distPath));\n\n  // fall through to index.html if the file doesn't exist\n  app.use(\"*\", (_req, res) => {\n    res.sendFile(path.resolve(distPath, \"index.html\"));\n  });\n}\n", "size_bytes": 2263}, "shared/schema.ts": {"content": "import { sql } from \"drizzle-orm\";\nimport { \n  pgTable, \n  text, \n  varchar, \n  integer, \n  timestamp, \n  json, \n  index,\n  pgEnum,\n  unique\n} from \"drizzle-orm/pg-core\";\nimport { createInsertSchema } from \"drizzle-zod\";\nimport { z } from \"zod\";\n\n// SquadJS Event Types\nexport const eventTypeEnum = pgEnum(\"event_type\", [\n  \"kill\", \n  \"death\", \n  \"revive\", \n  \"join\", \n  \"leave\", \n  \"respawn\", \n  \"round_start\", \n  \"round_end\"\n]);\n\nexport const periodTypeEnum = pgEnum(\"period_type\", [\n  \"all\",\n  \"week\", \n  \"month\"\n]);\n\n// Player table - tracks unique players by Steam ID\nexport const players = pgTable(\"players\", {\n  id: integer(\"id\").primaryKey().generatedByDefaultAsIdentity(),\n  steamId64: varchar(\"steam_id_64\", { length: 17 }).notNull().unique(),\n  eosId: varchar(\"eos_id\", { length: 32 }),\n  displayName: varchar(\"display_name\", { length: 100 }).notNull(),\n  firstSeenAt: timestamp(\"first_seen_at\").notNull().defaultNow(),\n  lastSeenAt: timestamp(\"last_seen_at\").notNull().defaultNow(),\n}, (table) => ({\n  steamIdIdx: index(\"players_steam_id_idx\").on(table.steamId64),\n  lastSeenIdx: index(\"players_last_seen_idx\").on(table.lastSeenAt),\n}));\n\n// Event log - all game events with metadata\nexport const events = pgTable(\"events\", {\n  id: integer(\"id\").primaryKey().generatedByDefaultAsIdentity(),\n  type: eventTypeEnum(\"type\").notNull(),\n  timestamp: timestamp(\"timestamp\").notNull().defaultNow(),\n  serverId: integer(\"server_id\"),\n  actorSteamId: varchar(\"actor_steam_id\", { length: 17 }),\n  targetSteamId: varchar(\"target_steam_id\", { length: 17 }),\n  meta: json(\"meta\").$type<{\n    weapon?: string;\n    headshot?: boolean;\n    map?: string;\n    squad?: string;\n    location?: { x: number; y: number; z: number };\n    damage?: number;\n  }>(),\n}, (table) => ({\n  typeTimestampIdx: index(\"events_type_timestamp_idx\").on(table.type, table.timestamp),\n  actorIdx: index(\"events_actor_idx\").on(table.actorSteamId),\n  targetIdx: index(\"events_target_idx\").on(table.targetSteamId),\n  timestampIdx: index(\"events_timestamp_idx\").on(table.timestamp),\n  serverIdIdx: index(\"events_server_id_idx\").on(table.serverId),\n}));\n\n// Player stats cache for faster leaderboard queries\nexport const playerStats = pgTable(\"player_stats\", {\n  id: integer(\"id\").primaryKey().generatedByDefaultAsIdentity(),\n  playerId: integer(\"player_id\").notNull().references(() => players.id),\n  periodType: periodTypeEnum(\"period_type\").notNull(),\n  since: timestamp(\"since\").notNull(),\n  kills: integer(\"kills\").notNull().default(0),\n  deaths: integer(\"deaths\").notNull().default(0),\n  revives: integer(\"revives\").notNull().default(0),\n  revived: integer(\"revived\").notNull().default(0),\n  updatedAt: timestamp(\"updated_at\").notNull().defaultNow(),\n}, (table) => ({\n  playerPeriodIdx: index(\"player_stats_player_period_idx\").on(table.playerId, table.periodType),\n  sinceIdx: index(\"player_stats_since_idx\").on(table.since),\n  playerPeriodUnique: unique(\"player_stats_player_period_unique\").on(table.playerId, table.periodType),\n}));\n\n// Steam account linking\nexport const steamLinks = pgTable(\"steam_links\", {\n  id: integer(\"id\").primaryKey().generatedByDefaultAsIdentity(),\n  steamId64: varchar(\"steam_id_64\", { length: 17 }).notNull().unique(),\n  pluginUserId: varchar(\"plugin_user_id\", { length: 100 }),\n  linkedAt: timestamp(\"linked_at\").notNull().defaultNow(),\n}, (table) => ({\n  steamIdIdx: index(\"steam_links_steam_id_idx\").on(table.steamId64),\n  userIdIdx: index(\"steam_links_user_id_idx\").on(table.pluginUserId),\n}));\n\n// Legacy user table (keeping for auth integration)\nexport const users = pgTable(\"users\", {\n  id: varchar(\"id\").primaryKey().default(sql`gen_random_uuid()`),\n  username: text(\"username\").notNull().unique(),\n  password: text(\"password\").notNull(),\n});\n\n// Zod schemas for validation\nexport const insertPlayerSchema = createInsertSchema(players).omit({\n  id: true,\n  firstSeenAt: true,\n  lastSeenAt: true,\n});\n\nexport const insertEventSchema = createInsertSchema(events).omit({\n  id: true,\n  timestamp: true,\n});\n\nexport const insertPlayerStatsSchema = createInsertSchema(playerStats).omit({\n  id: true,\n  updatedAt: true,\n});\n\nexport const insertSteamLinkSchema = createInsertSchema(steamLinks).omit({\n  id: true,\n  linkedAt: true,\n});\n\nexport const insertUserSchema = createInsertSchema(users).pick({\n  username: true,\n  password: true,\n});\n\n// Type exports\nexport type Player = typeof players.$inferSelect;\nexport type InsertPlayer = z.infer<typeof insertPlayerSchema>;\n\nexport type Event = typeof events.$inferSelect;\nexport type InsertEvent = z.infer<typeof insertEventSchema>;\n\nexport type PlayerStats = typeof playerStats.$inferSelect;\nexport type InsertPlayerStats = z.infer<typeof insertPlayerStatsSchema>;\n\nexport type SteamLink = typeof steamLinks.$inferSelect;\nexport type InsertSteamLink = z.infer<typeof insertSteamLinkSchema>;\n\nexport type User = typeof users.$inferSelect;\nexport type InsertUser = z.infer<typeof insertUserSchema>;\n", "size_bytes": 4947}, "client/src/App.tsx": {"content": "import { Switch, Route } from \"wouter\";\nimport { queryClient } from \"./lib/queryClient\";\nimport { QueryClientProvider } from \"@tanstack/react-query\";\nimport { Toaster } from \"@/components/ui/toaster\";\nimport { TooltipProvider } from \"@/components/ui/tooltip\";\nimport { SidebarProvider, SidebarTrigger } from \"@/components/ui/sidebar\";\nimport { AppSidebar } from \"@/components/AppSidebar\";\nimport ThemeToggle from \"@/components/ThemeToggle\";\nimport Dashboard from \"@/pages/Dashboard\";\nimport NotFound from \"@/pages/not-found\";\n\nfunction Router() {\n  return (\n    <Switch>\n      <Route path=\"/\" component={Dashboard} />\n      <Route path=\"/players\" component={Dashboard} />\n      <Route path=\"/leaderboards\" component={Dashboard} />\n      <Route path=\"/events\" component={Dashboard} />\n      <Route path=\"/analytics\" component={Dashboard} />\n      <Route path=\"/settings\" component={Dashboard} />\n      <Route component={NotFound} />\n    </Switch>\n  );\n}\n\nfunction App() {\n  const style = {\n    \"--sidebar-width\": \"20rem\",\n    \"--sidebar-width-icon\": \"4rem\",\n  };\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      <TooltipProvider>\n        <SidebarProvider style={style as React.CSSProperties}>\n          <div className=\"flex h-screen w-full\">\n            <AppSidebar />\n            <div className=\"flex flex-col flex-1\">\n              <header className=\"flex items-center justify-between p-4 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n                <SidebarTrigger data-testid=\"button-sidebar-toggle\" />\n                <ThemeToggle />\n              </header>\n              <main className=\"flex-1 overflow-auto\">\n                <Router />\n              </main>\n            </div>\n          </div>\n          <Toaster />\n        </SidebarProvider>\n      </TooltipProvider>\n    </QueryClientProvider>\n  );\n}\n\nexport default App;\n", "size_bytes": 1887}, "client/src/index.css": {"content": "@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* LIGHT MODE */\n:root {\n  --button-outline: rgba(0,0,0, .10);\n  --badge-outline: rgba(0,0,0, .05);\n\n  /* Automatic computation of border around primary / danger buttons */\n  --opaque-button-border-intensity: -8; /* In terms of percentages */\n\n  /* Backgrounds applied on top of other backgrounds when hovered/active */\n  --elevate-1: rgba(0,0,0, .03);\n  --elevate-2: rgba(0,0,0, .08);\n\n  --background: 220 20% 97%;\n\n  --foreground: 220 15% 15%;\n\n  --border: 220 15% 88%;\n\n  --card: 220 15% 100%;\n\n  --card-foreground: 220 15% 15%;\n\n  --card-border: 220 15% 93%;\n\n  --sidebar: 220 18% 95%;\n\n  --sidebar-foreground: 220 15% 15%;\n\n  --sidebar-border: 220 15% 90%;\n\n  --sidebar-primary: 200 90% 45%;\n\n  --sidebar-primary-foreground: 220 15% 98%;\n\n  --sidebar-accent: 220 15% 92%;\n\n  --sidebar-accent-foreground: 220 15% 15%;\n\n  --sidebar-ring: 200 90% 45%;\n\n  --popover: 220 15% 98%;\n\n  --popover-foreground: 220 15% 15%;\n\n  --popover-border: 220 15% 90%;\n\n  --primary: 200 90% 45%;\n\n  --primary-foreground: 220 15% 98%;\n\n  --secondary: 220 15% 94%;\n\n  --secondary-foreground: 220 15% 15%;\n\n  --muted: 220 12% 96%;\n\n  --muted-foreground: 220 10% 45%;\n\n  --accent: 220 10% 94%;\n\n  --accent-foreground: 220 15% 15%;\n\n  --destructive: 0 70% 55%;\n\n  --destructive-foreground: 0 0% 98%;\n\n  --input: 220 15% 75%;\n  --ring: 200 90% 45%;\n  --chart-1: 200 90% 35%;\n  --chart-2: 120 60% 40%;\n  --chart-3: 45 100% 50%;\n  --chart-4: 280 70% 45%;\n  --chart-5: 15 80% 45%;\n\n  --font-sans: Inter, sans-serif;\n  --font-serif: Georgia, serif;\n  --font-mono: JetBrains Mono, monospace;\n  --radius: .5rem; /* 8px */\n  --shadow-2xs: 0px 2px 0px 0px hsl(220 15% 88% / 0.00);\n  --shadow-xs: 0px 2px 0px 0px hsl(220 15% 88% / 0.00);\n  --shadow-sm: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 1px 2px -1px hsl(220 15% 88% / 0.00);\n  --shadow: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 1px 2px -1px hsl(220 15% 88% / 0.00);\n  --shadow-md: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 2px 4px -1px hsl(220 15% 88% / 0.00);\n  --shadow-lg: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 4px 6px -1px hsl(220 15% 88% / 0.00);\n  --shadow-xl: 0px 2px 0px 0px hsl(220 15% 88% / 0.00), 0px 8px 10px -1px hsl(220 15% 88% / 0.00);\n  --shadow-2xl: 0px 2px 0px 0px hsl(220 15% 88% / 0.00);\n  --tracking-normal: 0em;\n  --spacing: 0.25rem;\n\n  /* Automatically computed borders - intensity can be controlled by the user by the --opaque-button-border-intensity setting */\n\n  /* Fallback for older browsers */\n  --sidebar-primary-border: hsl(var(--sidebar-primary));\n  --sidebar-primary-border: hsl(from hsl(var(--sidebar-primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n\n  /* Fallback for older browsers */\n  --sidebar-accent-border: hsl(var(--sidebar-accent));\n  --sidebar-accent-border: hsl(from hsl(var(--sidebar-accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n\n  /* Fallback for older browsers */\n  --primary-border: hsl(var(--primary));\n  --primary-border: hsl(from hsl(var(--primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n\n  /* Fallback for older browsers */\n  --secondary-border: hsl(var(--secondary));\n  --secondary-border: hsl(from hsl(var(--secondary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n\n  /* Fallback for older browsers */\n  --muted-border: hsl(var(--muted));\n  --muted-border: hsl(from hsl(var(--muted)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n\n  /* Fallback for older browsers */\n  --accent-border: hsl(var(--accent));\n  --accent-border: hsl(from hsl(var(--accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n\n  /* Fallback for older browsers */\n  --destructive-border: hsl(var(--destructive));\n  --destructive-border: hsl(from hsl(var(--destructive)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);\n}\n\n.dark {\n  --button-outline: rgba(255,255,255, .10);\n  --badge-outline: rgba(255,255,255, .05);\n\n  --opaque-button-border-intensity: 9;  /* In terms of percentages */\n\n  /* Backgrounds applied on top of other backgrounds when hovered/active */\n  --elevate-1: rgba(255,255,255, .04);\n  --elevate-2: rgba(255,255,255, .09);\n\n  --background: 220 15% 8%;\n\n  --foreground: 220 15% 95%;\n\n  --border: 220 12% 18%;\n\n  --card: 220 12% 12%;\n\n  --card-foreground: 220 15% 95%;\n\n  --card-border: 220 10% 20%;\n\n  --sidebar: 220 15% 10%;\n\n  --sidebar-foreground: 220 15% 95%;\n\n  --sidebar-border: 220 12% 18%;\n\n  --sidebar-primary: 200 100% 60%;\n\n  --sidebar-primary-foreground: 220 15% 8%;\n\n  --sidebar-accent: 220 10% 16%;\n\n  --sidebar-accent-foreground: 220 15% 90%;\n\n  --sidebar-ring: 200 100% 60%;\n\n  --popover: 220 10% 16%;\n\n  --popover-foreground: 220 15% 95%;\n\n  --popover-border: 220 10% 24%;\n\n  --primary: 200 100% 60%;\n\n  --primary-foreground: 220 15% 8%;\n\n  --secondary: 220 10% 20%;\n\n  --secondary-foreground: 220 15% 90%;\n\n  --muted: 220 8% 14%;\n\n  --muted-foreground: 220 10% 70%;\n\n  --accent: 220 8% 16%;\n\n  --accent-foreground: 220 15% 90%;\n\n  --destructive: 0 70% 55%;\n\n  --destructive-foreground: 0 0% 98%;\n\n  --input: 220 10% 25%;\n  --ring: 200 100% 60%;\n  --chart-1: 200 100% 70%;\n  --chart-2: 120 60% 60%;\n  --chart-3: 45 100% 65%;\n  --chart-4: 280 70% 65%;\n  --chart-5: 15 80% 65%;\n\n  --shadow-2xs: 0px 2px 0px 0px hsl(220 15% 5% / 0.00);\n  --shadow-xs: 0px 2px 0px 0px hsl(220 15% 5% / 0.00);\n  --shadow-sm: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 1px 2px -1px hsl(220 15% 5% / 0.00);\n  --shadow: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 1px 2px -1px hsl(220 15% 5% / 0.00);\n  --shadow-md: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 2px 4px -1px hsl(220 15% 5% / 0.00);\n  --shadow-lg: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 4px 6px -1px hsl(220 15% 5% / 0.00);\n  --shadow-xl: 0px 2px 0px 0px hsl(220 15% 5% / 0.00), 0px 8px 10px -1px hsl(220 15% 5% / 0.00);\n  --shadow-2xl: 0px 2px 0px 0px hsl(220 15% 5% / 0.00);\n\n}\n\n@layer base {\n  * {\n    @apply border-border;\n  }\n\n  body {\n    @apply font-sans antialiased bg-background text-foreground;\n  }\n}\n\n/**\n * Using the elevate system.\n * Automatic contrast adjustment.\n *\n * <element className=\"hover-elevate\" />\n * <element className=\"active-elevate-2\" />\n *\n * // Using the tailwind utility when a data attribute is \"on\"\n * <element className=\"toggle-elevate data-[state=on]:toggle-elevated\" />\n * // Or manually controlling the toggle state\n * <element className=\"toggle-elevate toggle-elevated\" />\n *\n * Elevation systems have to handle many states.\n * - not-hovered, vs. hovered vs. active  (three mutually exclusive states)\n * - toggled or not\n * - focused or not (this is not handled with these utilities)\n *\n * Even without handling focused or not, this is six possible combinations that\n * need to be distinguished from eachother visually.\n */\n@layer utilities {\n\n  /* Hide ugly search cancel button in Chrome until we can style it properly */\n  input[type=\"search\"]::-webkit-search-cancel-button {\n    @apply hidden;\n  }\n\n  /* Placeholder styling for contentEditable div */\n  [contenteditable][data-placeholder]:empty::before {\n    content: attr(data-placeholder);\n    color: hsl(var(--muted-foreground));\n    pointer-events: none;\n  }\n\n  /* .no-default-hover-elevate/no-default-active-elevate is an escape hatch so consumers of\n   * buttons/badges can remove the automatic brightness adjustment on interactions\n   * and program their own. */\n  .no-default-hover-elevate {}\n\n  .no-default-active-elevate {}\n\n\n  /**\n   * Toggleable backgrounds go behind the content. Hoverable/active goes on top.\n   * This way they can stack/compound. Both will overlap the parent's borders!\n   * So borders will be automatically adjusted both on toggle, and hover/active,\n   * and they will be compounded.\n   */\n  .toggle-elevate::before,\n  .toggle-elevate-2::before {\n    content: \"\";\n    pointer-events: none;\n    position: absolute;\n    inset: 0px;\n    /*border-radius: inherit;   match rounded corners */\n    border-radius: inherit;\n    z-index: -1;\n    /* sits behind content but above backdrop */\n  }\n\n  .toggle-elevate.toggle-elevated::before {\n    background-color: var(--elevate-2);\n  }\n\n  /* If there's a 1px border, adjust the inset so that it covers that parent's border */\n  .border.toggle-elevate::before {\n    inset: -1px;\n  }\n\n  /* Does not work on elements with overflow:hidden! */\n  .hover-elevate:not(.no-default-hover-elevate),\n  .active-elevate:not(.no-default-active-elevate),\n  .hover-elevate-2:not(.no-default-hover-elevate),\n  .active-elevate-2:not(.no-default-active-elevate) {\n    position: relative;\n    z-index: 0;\n  }\n\n  .hover-elevate:not(.no-default-hover-elevate)::after,\n  .active-elevate:not(.no-default-active-elevate)::after,\n  .hover-elevate-2:not(.no-default-hover-elevate)::after,\n  .active-elevate-2:not(.no-default-active-elevate)::after {\n    content: \"\";\n    pointer-events: none;\n    position: absolute;\n    inset: 0px;\n    /*border-radius: inherit;   match rounded corners */\n    border-radius: inherit;\n    z-index: 999;\n    /* sits in front of content */\n  }\n\n  .hover-elevate:hover:not(.no-default-hover-elevate)::after,\n  .active-elevate:active:not(.no-default-active-elevate)::after {\n    background-color: var(--elevate-1);\n  }\n\n  .hover-elevate-2:hover:not(.no-default-hover-elevate)::after,\n  .active-elevate-2:active:not(.no-default-active-elevate)::after {\n    background-color: var(--elevate-2);\n  }\n\n  /* If there's a 1px border, adjust the inset so that it covers that parent's border */\n  .border.hover-elevate:not(.no-hover-interaction-elevate)::after,\n  .border.active-elevate:not(.no-active-interaction-elevate)::after,\n  .border.hover-elevate-2:not(.no-hover-interaction-elevate)::after,\n  .border.active-elevate-2:not(.no-active-interaction-elevate)::after,\n  .border.hover-elevate:not(.no-hover-interaction-elevate)::after {\n    inset: -1px;\n  }\n}", "size_bytes": 9864}, "client/src/main.tsx": {"content": "import { createRoot } from \"react-dom/client\";\nimport App from \"./App\";\nimport \"./index.css\";\n\ncreateRoot(document.getElementById(\"root\")!).render(<App />);\n", "size_bytes": 157}, "plugins/web-dashboard/index.js": {"content": "const axios = require('axios');\n\nmodule.exports = class WebDashboard {\n  static get description() {\n    return 'SquadJS Web Dashboard Plugin - Sends game events to external dashboard API for real-time monitoring and statistics';\n  }\n\n  static get defaultEnabled() {\n    return false;\n  }\n\n  static get optionsSpecification() {\n    return {\n      dashboardApiUrl: {\n        required: true,\n        description: 'Base URL of the dashboard API (e.g., https://your-replit-url)',\n        default: 'https://0f867722-47fe-47b2-b2c5-4b785a3cfe64-00-jbbkebkcbr2.worf.replit.dev'\n      },\n      apiTimeout: {\n        required: false,\n        description: 'API request timeout in milliseconds',\n        default: 5000\n      },\n      serverId: {\n        required: false,\n        description: 'Unique server identifier for multi-server deployments',\n        default: 1\n      },\n      enableDebugLogging: {\n        required: false,\n        description: 'Enable debug logging for troubleshooting',\n        default: false\n      },\n      retryAttempts: {\n        required: false,\n        description: 'Number of retry attempts for failed API calls',\n        default: 3\n      }\n    };\n  }\n\n  constructor(server, options, connectors) {\n    this.server = server;\n    this.options = options;\n    this.connectors = connectors;\n    \n    // HTTP client configuration\n    this.apiClient = axios.create({\n      baseURL: this.options.dashboardApiUrl,\n      timeout: this.options.apiTimeout,\n      headers: {\n        'Content-Type': 'application/json',\n        'User-Agent': 'SquadJS-WebDashboard/1.0.0'\n      }\n    });\n\n    // Event handler bindings\n    this.onPlayerConnected = this.onPlayerConnected.bind(this);\n    this.onPlayerDisconnected = this.onPlayerDisconnected.bind(this);\n    this.onPlayerDied = this.onPlayerDied.bind(this);\n    this.onPlayerRevived = this.onPlayerRevived.bind(this);\n    this.onPlayerSpawn = this.onPlayerSpawn.bind(this);\n    this.onNewGame = this.onNewGame.bind(this);\n    this.onRoundEnded = this.onRoundEnded.bind(this);\n  }\n\n  async mount() {\n    this.verbose(1, 'Mounting Web Dashboard plugin...');\n    \n    // Test API connectivity\n    try {\n      await this.testApiConnection();\n      this.verbose(1, 'Dashboard API connection successful');\n    } catch (error) {\n      this.verbose(1, `Warning: Cannot connect to dashboard API: ${error.message}`);\n    }\n\n    // Register event listeners using correct SquadJS event names\n    this.server.on('PLAYER_CONNECTED', this.onPlayerConnected);\n    this.server.on('PLAYER_DISCONNECTED', this.onPlayerDisconnected);\n    this.server.on('PLAYER_DIED', this.onPlayerDied);\n    this.server.on('PLAYER_REVIVED', this.onPlayerRevived);\n    this.server.on('PLAYER_SPAWN', this.onPlayerSpawn);\n    this.server.on('NEW_GAME', this.onNewGame);\n    this.server.on('ROUND_ENDED', this.onRoundEnded);\n\n    this.verbose(1, 'Web Dashboard plugin mounted successfully');\n  }\n\n  async unmount() {\n    this.verbose(1, 'Unmounting Web Dashboard plugin...');\n    \n    // Remove event listeners using correct method names\n    this.server.off('PLAYER_CONNECTED', this.onPlayerConnected);\n    this.server.off('PLAYER_DISCONNECTED', this.onPlayerDisconnected);\n    this.server.off('PLAYER_DIED', this.onPlayerDied);\n    this.server.off('PLAYER_REVIVED', this.onPlayerRevived);\n    this.server.off('PLAYER_SPAWN', this.onPlayerSpawn);\n    this.server.off('NEW_GAME', this.onNewGame);\n    this.server.off('ROUND_ENDED', this.onRoundEnded);\n\n    this.verbose(1, 'Web Dashboard plugin unmounted');\n  }\n\n  async testApiConnection() {\n    const response = await this.apiClient.get('/api/server');\n    return response.status === 200;\n  }\n\n  async sendEventToAPI(eventData, attemptNumber = 1) {\n    try {\n      const response = await this.apiClient.post('/api/events', eventData);\n      \n      if (this.options.enableDebugLogging) {\n        this.verbose(3, `Event sent successfully: ${eventData.type} (ID: ${response.data.event?.id})`);\n      }\n      \n      return response.data;\n    } catch (error) {\n      this.verbose(1, `Failed to send event to dashboard API (attempt ${attemptNumber}): ${error.message}`);\n      \n      // Retry logic for failed requests with exponential backoff\n      if (error.response?.status >= 500 && attemptNumber <= this.options.retryAttempts) {\n        const backoffMs = Math.min(1000 * Math.pow(2, attemptNumber - 1), 10000); // Cap at 10 seconds\n        this.verbose(2, `Retrying API call in ${backoffMs}ms (attempt ${attemptNumber + 1}/${this.options.retryAttempts + 1})`);\n        \n        return new Promise((resolve, reject) => {\n          setTimeout(async () => {\n            try {\n              const result = await this.sendEventToAPI(eventData, attemptNumber + 1);\n              resolve(result);\n            } catch (retryError) {\n              reject(retryError);\n            }\n          }, backoffMs);\n        });\n      }\n      \n      throw error;\n    }\n  }\n\n  // Event Handlers\n  async onPlayerConnected(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'join',\n        actorSteamId: info.player.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.player.currentLayer || 'Unknown',\n          playerName: info.player.name,\n          eosId: info.player.eosID,\n          teamId: info.player.teamID,\n          squadId: info.player.squadID\n        }\n      });\n\n      this.verbose(2, `Player connected: ${info.player.name} (${info.player.steamID})`);\n    } catch (error) {\n      this.verbose(1, `Error handling player connection: ${error.message}`);\n    }\n  }\n\n  async onPlayerDisconnected(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'leave',\n        actorSteamId: info.player.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.player.currentLayer || 'Unknown',\n          playerName: info.player.name,\n          eosId: info.player.eosID\n        }\n      });\n\n      this.verbose(2, `Player disconnected: ${info.player.name} (${info.player.steamID})`);\n    } catch (error) {\n      this.verbose(1, `Error handling player disconnection: ${error.message}`);\n    }\n  }\n\n  async onPlayerDied(info) {\n    try {\n      // Handle both regular kills and suicides/teamkills\n      const attackerSteamId = info.attacker?.steamID || null;\n      const victimSteamId = info.victim?.steamID;\n\n      await this.sendEventToAPI({\n        type: 'kill',\n        actorSteamId: attackerSteamId,\n        targetSteamId: victimSteamId,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.victim?.currentLayer || 'Unknown',\n          weapon: info.weapon || 'Unknown',\n          headshot: info.headshot || false,\n          killerName: info.attacker?.name || null,\n          victimName: info.victim?.name,\n          killerEOS: info.attacker?.eosID || null,\n          victimEOS: info.victim?.eosID,\n          teamkill: info.teamkill || false,\n          suicide: !info.attacker || info.attacker.steamID === info.victim.steamID\n        }\n      });\n\n      const logMessage = attackerSteamId \n        ? `Kill: ${info.attacker?.name} -> ${info.victim?.name} (${info.weapon})`\n        : `Death: ${info.victim?.name} (suicide/environment)`;\n      \n      this.verbose(2, logMessage);\n    } catch (error) {\n      this.verbose(1, `Error handling player death: ${error.message}`);\n    }\n  }\n\n  async onPlayerRevived(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'revive',\n        actorSteamId: info.reviver?.steamID,\n        targetSteamId: info.victim?.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.victim?.currentLayer || 'Unknown',\n          reviverName: info.reviver?.name,\n          victimName: info.victim?.name,\n          reviverEOS: info.reviver?.eosID,\n          victimEOS: info.victim?.eosID\n        }\n      });\n\n      this.verbose(2, `Revive: ${info.reviver?.name} revived ${info.victim?.name}`);\n    } catch (error) {\n      this.verbose(1, `Error handling player revive: ${error.message}`);\n    }\n  }\n\n  async onPlayerSpawn(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'respawn',\n        actorSteamId: info.player?.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.player?.currentLayer || 'Unknown',\n          playerName: info.player?.name,\n          eosId: info.player?.eosID,\n          teamId: info.player?.teamID,\n          squadId: info.player?.squadID,\n          role: info.player?.role\n        }\n      });\n\n      this.verbose(3, `Player spawned: ${info.player?.name}`);\n    } catch (error) {\n      this.verbose(1, `Error handling player spawn: ${error.message}`);\n    }\n  }\n\n  async onNewGame(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'round_start',\n        serverId: this.options.serverId,\n        meta: {\n          map: info.layer || info.currentLayer || 'Unknown',\n          gamemode: info.gamemode || 'Unknown'\n        }\n      });\n\n      this.verbose(2, `Round started: ${info.layer || 'Unknown'}`);\n    } catch (error) {\n      this.verbose(1, `Error handling round start: ${error.message}`);\n    }\n  }\n\n  async onRoundEnded(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'round_end',\n        serverId: this.options.serverId,\n        meta: {\n          map: info.layer || info.currentLayer || 'Unknown',\n          winner: info.winner || 'Unknown',\n          duration: info.duration || 0\n        }\n      });\n\n      this.verbose(2, `Round ended: ${info.layer || 'Unknown'} - Winner: ${info.winner || 'Unknown'}`);\n    } catch (error) {\n      this.verbose(1, `Error handling round end: ${error.message}`);\n    }\n  }\n\n  verbose(level, message) {\n    if (this.server.logger) {\n      this.server.logger(level, 'WebDashboard', message);\n    } else {\n      console.log(`[WebDashboard] ${message}`);\n    }\n  }\n};", "size_bytes": 9824}, "client/src/components/AppSidebar.tsx": {"content": "import {\n  Sidebar,\n  SidebarContent,\n  SidebarGroup,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarMenu,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarHeader,\n  SidebarFooter,\n} from \"@/components/ui/sidebar\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Monitor, Users, Trophy, Activity, Settings, BarChart3 } from \"lucide-react\";\nimport { Link, useLocation } from \"wouter\";\n\nconst menuItems = [\n  {\n    title: \"Dashboard\",\n    url: \"/\",\n    icon: Monitor,\n  },\n  {\n    title: \"Players\",\n    url: \"/players\", \n    icon: Users,\n  },\n  {\n    title: \"Leaderboards\",\n    url: \"/leaderboards\",\n    icon: Trophy,\n  },\n  {\n    title: \"Live Events\",\n    url: \"/events\",\n    icon: Activity,\n  },\n  {\n    title: \"Analytics\",\n    url: \"/analytics\",\n    icon: BarChart3,\n  },\n  {\n    title: \"Settings\",\n    url: \"/settings\",\n    icon: Settings,\n  },\n];\n\nexport function AppSidebar() {\n  const [location] = useLocation();\n\n  return (\n    <Sidebar data-testid=\"sidebar-main\">\n      <SidebarHeader className=\"p-4\">\n        <div className=\"flex items-center gap-2\">\n          <Monitor className=\"w-6 h-6 text-primary\" />\n          <div>\n            <h2 className=\"font-bold text-lg\">SquadJS</h2>\n            <p className=\"text-xs text-muted-foreground\">Dashboard</p>\n          </div>\n        </div>\n      </SidebarHeader>\n      \n      <SidebarContent>\n        <SidebarGroup>\n          <SidebarGroupLabel>Navigation</SidebarGroupLabel>\n          <SidebarGroupContent>\n            <SidebarMenu>\n              {menuItems.map((item) => (\n                <SidebarMenuItem key={item.title}>\n                  <SidebarMenuButton \n                    asChild \n                    isActive={location === item.url}\n                    data-testid={`sidebar-item-${item.title.toLowerCase().replace(/\\s+/g, '-')}`}\n                  >\n                    <Link href={item.url}>\n                      <item.icon className=\"w-4 h-4\" />\n                      <span>{item.title}</span>\n                    </Link>\n                  </SidebarMenuButton>\n                </SidebarMenuItem>\n              ))}\n            </SidebarMenu>\n          </SidebarGroupContent>\n        </SidebarGroup>\n        \n        <SidebarGroup>\n          <SidebarGroupLabel>Server Status</SidebarGroupLabel>\n          <SidebarGroupContent>\n            <div className=\"px-3 py-2 space-y-2\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span>Status</span>\n                <Badge variant=\"secondary\" className=\"bg-green-500/20 text-green-600\">\n                  Online\n                </Badge>\n              </div>\n              <div className=\"flex items-center justify-between text-sm\">\n                <span>Players</span>\n                <span className=\"font-medium\">42/96</span>\n              </div>\n              <div className=\"flex items-center justify-between text-sm\">\n                <span>Uptime</span>\n                <span className=\"font-medium\">6h 23m</span>\n              </div>\n            </div>\n          </SidebarGroupContent>\n        </SidebarGroup>\n      </SidebarContent>\n      \n      <SidebarFooter className=\"p-4\">\n        <div className=\"text-xs text-muted-foreground\">\n          <p>SquadJS Plugin v1.0.0</p>\n          <p>Last sync: {new Date().toLocaleTimeString()}</p>\n        </div>\n      </SidebarFooter>\n    </Sidebar>\n  );\n}", "size_bytes": 3369}, "client/src/components/KillFeed.tsx": {"content": "import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Skull, Heart, UserMinus, UserPlus } from \"lucide-react\";\nimport { ScrollArea } from \"@/components/ui/scroll-area\";\n\ninterface KillFeedEvent {\n  id: string;\n  type: \"kill\" | \"death\" | \"revive\" | \"join\" | \"leave\";\n  timestamp: string;\n  actor?: string;\n  target?: string;\n  weapon?: string;\n  headshot?: boolean;\n}\n\ninterface KillFeedProps {\n  events: KillFeedEvent[];\n  maxEvents?: number;\n}\n\nexport default function KillFeed({ events, maxEvents = 20 }: KillFeedProps) {\n  const getEventIcon = (type: string) => {\n    switch (type) {\n      case \"kill\": return <Skull className=\"w-3 h-3 text-red-500\" />;\n      case \"revive\": return <Heart className=\"w-3 h-3 text-green-500\" />;\n      case \"join\": return <UserPlus className=\"w-3 h-3 text-blue-500\" />;\n      case \"leave\": return <UserMinus className=\"w-3 h-3 text-gray-500\" />;\n      default: return <Skull className=\"w-3 h-3\" />;\n    }\n  };\n\n  const formatEvent = (event: KillFeedEvent) => {\n    switch (event.type) {\n      case \"kill\":\n        return (\n          <div className=\"flex items-center gap-2 text-sm\">\n            <span className=\"font-medium\">{event.actor}</span>\n            <span className=\"text-muted-foreground\">→</span>\n            <span>{event.target}</span>\n            {event.weapon && (\n              <Badge variant=\"outline\" className=\"text-xs\">\n                {event.weapon}\n              </Badge>\n            )}\n            {event.headshot && (\n              <Badge variant=\"destructive\" className=\"text-xs\">\n                HS\n              </Badge>\n            )}\n          </div>\n        );\n      case \"revive\":\n        return (\n          <div className=\"flex items-center gap-2 text-sm\">\n            <span className=\"font-medium\">{event.actor}</span>\n            <span className=\"text-muted-foreground\">revived</span>\n            <span>{event.target}</span>\n          </div>\n        );\n      case \"join\":\n        return (\n          <div className=\"flex items-center gap-2 text-sm\">\n            <span className=\"font-medium\">{event.actor}</span>\n            <span className=\"text-muted-foreground\">joined the server</span>\n          </div>\n        );\n      case \"leave\":\n        return (\n          <div className=\"flex items-center gap-2 text-sm\">\n            <span className=\"font-medium\">{event.actor}</span>\n            <span className=\"text-muted-foreground\">left the server</span>\n          </div>\n        );\n      default:\n        return <span className=\"text-sm text-muted-foreground\">Unknown event</span>;\n    }\n  };\n\n  const formatTime = (timestamp: string) => {\n    const date = new Date(timestamp);\n    return date.toLocaleTimeString('en-US', { \n      hour12: false, \n      hour: '2-digit', \n      minute: '2-digit', \n      second: '2-digit' \n    });\n  };\n\n  const displayEvents = events.slice(0, maxEvents);\n\n  return (\n    <Card data-testid=\"card-kill-feed\" className=\"hover-elevate\">\n      <CardHeader className=\"pb-3\">\n        <CardTitle className=\"text-lg flex items-center gap-2\">\n          <Skull className=\"w-5 h-5\" />\n          Live Feed\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <ScrollArea className=\"h-96\">\n          <div className=\"space-y-2\">\n            {displayEvents.map((event) => (\n              <div\n                key={event.id}\n                data-testid={`event-${event.id}`}\n                className=\"flex items-start gap-3 p-2 rounded-md hover-elevate border-l-2 border-muted\"\n              >\n                <div className=\"flex-shrink-0 mt-1\">\n                  {getEventIcon(event.type)}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  {formatEvent(event)}\n                  <p className=\"text-xs text-muted-foreground font-mono mt-1\">\n                    {formatTime(event.timestamp)}\n                  </p>\n                </div>\n              </div>\n            ))}\n            {displayEvents.length === 0 && (\n              <div className=\"text-center py-8 text-muted-foreground\">\n                <Skull className=\"w-8 h-8 mx-auto mb-2 opacity-50\" />\n                <p>No recent events</p>\n              </div>\n            )}\n          </div>\n        </ScrollArea>\n      </CardContent>\n    </Card>\n  );\n}", "size_bytes": 4338}, "client/src/components/Leaderboard.tsx": {"content": "import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Trophy, Medal, Award } from \"lucide-react\";\n\ninterface LeaderboardEntry {\n  rank: number;\n  name: string;\n  steamId: string;\n  value: number;\n  avatar?: string;\n}\n\ninterface LeaderboardProps {\n  title: string;\n  entries: LeaderboardEntry[];\n  metric: string;\n  period: \"week\" | \"month\" | \"all\";\n}\n\nexport default function Leaderboard({ title, entries, metric, period }: LeaderboardProps) {\n  const getRankIcon = (rank: number) => {\n    switch (rank) {\n      case 1: return <Trophy className=\"w-4 h-4 text-yellow-500\" />;\n      case 2: return <Medal className=\"w-4 h-4 text-gray-400\" />;\n      case 3: return <Award className=\"w-4 h-4 text-amber-600\" />;\n      default: return <span className=\"w-4 h-4 text-center text-xs font-bold text-muted-foreground\">{rank}</span>;\n    }\n  };\n\n  const getPeriodLabel = () => {\n    switch (period) {\n      case \"week\": return \"Last 7 days\";\n      case \"month\": return \"Last 30 days\";\n      case \"all\": return \"All time\";\n    }\n  };\n\n  return (\n    <Card data-testid={`card-leaderboard-${metric}-${period}`} className=\"hover-elevate\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-center justify-between\">\n          <CardTitle className=\"text-lg\" data-testid=\"text-leaderboard-title\">{title}</CardTitle>\n          <Badge variant=\"secondary\" data-testid=\"badge-leaderboard-period\">\n            {getPeriodLabel()}\n          </Badge>\n        </div>\n      </CardHeader>\n      <CardContent className=\"space-y-3\">\n        {entries.map((entry) => (\n          <div\n            key={`${entry.steamId}-${entry.rank}`}\n            data-testid={`row-leaderboard-${entry.rank}`}\n            className=\"flex items-center gap-3 p-2 rounded-md hover-elevate\"\n          >\n            <div className=\"flex-shrink-0\">\n              {getRankIcon(entry.rank)}\n            </div>\n            \n            <Avatar className=\"w-8 h-8\">\n              <AvatarImage src={entry.avatar} alt={entry.name} />\n              <AvatarFallback className=\"text-xs\">\n                {entry.name.substring(0, 2).toUpperCase()}\n              </AvatarFallback>\n            </Avatar>\n            \n            <div className=\"flex-1 min-w-0\">\n              <p className=\"font-medium truncate\" data-testid={`text-player-name-${entry.rank}`}>\n                {entry.name}\n              </p>\n              <p className=\"text-xs text-muted-foreground font-mono\" data-testid={`text-steamid-${entry.rank}`}>\n                {entry.steamId.substring(0, 12)}...\n              </p>\n            </div>\n            \n            <div className=\"text-right\">\n              <p className=\"font-bold\" data-testid={`text-value-${entry.rank}`}>\n                {entry.value}\n              </p>\n              <p className=\"text-xs text-muted-foreground\">{metric}</p>\n            </div>\n          </div>\n        ))}\n      </CardContent>\n    </Card>\n  );\n}", "size_bytes": 3067}, "client/src/components/PlayerCard.tsx": {"content": "import { Card } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\nimport { Button } from \"@/components/ui/button\";\nimport { ExternalLink, Skull, Heart, Target } from \"lucide-react\";\n\ninterface PlayerCardProps {\n  name: string;\n  steamId: string;\n  squad?: string;\n  role?: string;\n  score: number;\n  kills: number;\n  deaths: number;\n  revives: number;\n  status: \"online\" | \"offline\";\n  avatar?: string;\n  onSteamLink?: () => void;\n  onViewStats?: () => void;\n}\n\nexport default function PlayerCard({\n  name,\n  steamId,\n  squad,\n  role,\n  score,\n  kills,\n  deaths,\n  revives,\n  status,\n  avatar,\n  onSteamLink,\n  onViewStats\n}: PlayerCardProps) {\n  const kd = deaths > 0 ? (kills / deaths).toFixed(2) : kills.toString();\n  const statusColor = status === \"online\" ? \"bg-green-500\" : \"bg-gray-500\";\n\n  return (\n    <Card data-testid={`card-player-${steamId}`} className=\"p-4 hover-elevate\">\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center gap-3 flex-1 min-w-0\">\n          <div className=\"relative\">\n            <Avatar className=\"w-10 h-10\">\n              <AvatarImage src={avatar} alt={name} />\n              <AvatarFallback>{name.substring(0, 2).toUpperCase()}</AvatarFallback>\n            </Avatar>\n            <div className={`absolute -bottom-1 -right-1 w-3 h-3 rounded-full border-2 border-card ${statusColor}`} />\n          </div>\n          \n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center gap-2 mb-1\">\n              <h3 className=\"font-semibold truncate\" data-testid={`text-player-name-${steamId}`}>\n                {name}\n              </h3>\n              {squad && (\n                <Badge variant=\"outline\" className=\"text-xs\" data-testid={`badge-squad-${steamId}`}>\n                  {squad}\n                </Badge>\n              )}\n            </div>\n            <div className=\"flex items-center gap-4 text-xs text-muted-foreground\">\n              <span className=\"font-mono\" data-testid={`text-steamid-${steamId}`}>\n                {steamId.substring(0, 8)}...\n              </span>\n              {role && <span>{role}</span>}\n              <span data-testid={`text-score-${steamId}`}>Score: {score}</span>\n            </div>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center gap-4\">\n          <div className=\"flex gap-3 text-sm\">\n            <div className=\"flex items-center gap-1\" data-testid={`stat-kills-${steamId}`}>\n              <Skull className=\"w-3 h-3 text-red-500\" />\n              <span>{kills}</span>\n            </div>\n            <div className=\"flex items-center gap-1\" data-testid={`stat-deaths-${steamId}`}>\n              <Target className=\"w-3 h-3 text-gray-500\" />\n              <span>{deaths}</span>\n            </div>\n            <div className=\"flex items-center gap-1\" data-testid={`stat-revives-${steamId}`}>\n              <Heart className=\"w-3 h-3 text-green-500\" />\n              <span>{revives}</span>\n            </div>\n            <div className=\"text-muted-foreground\" data-testid={`stat-kd-${steamId}`}>\n              K/D: {kd}\n            </div>\n          </div>\n          \n          <div className=\"flex gap-2\">\n            <Button\n              size=\"sm\"\n              variant=\"outline\"\n              onClick={onSteamLink}\n              data-testid={`button-steam-link-${steamId}`}\n            >\n              <ExternalLink className=\"w-3 h-3\" />\n            </Button>\n            <Button\n              size=\"sm\"\n              onClick={onViewStats}\n              data-testid={`button-view-stats-${steamId}`}\n            >\n              Stats\n            </Button>\n          </div>\n        </div>\n      </div>\n    </Card>\n  );\n}", "size_bytes": 3805}, "client/src/components/ServerHeader.tsx": {"content": "import { Badge } from \"@/components/ui/badge\";\nimport { Card } from \"@/components/ui/card\";\nimport { Activity, Users, Globe, Clock } from \"lucide-react\";\n\ninterface ServerHeaderProps {\n  name: string;\n  map: string;\n  playersCount: number;\n  maxPlayers: number;\n  status: \"online\" | \"offline\";\n  lastUpdate?: string;\n}\n\nexport default function ServerHeader({\n  name,\n  map,\n  playersCount,\n  maxPlayers,\n  status,\n  lastUpdate\n}: ServerHeaderProps) {\n  const statusColor = status === \"online\" ? \"bg-green-500\" : \"bg-red-500\";\n  \n  return (\n    <Card data-testid=\"card-server-header\" className=\"p-6 hover-elevate\">\n      <div className=\"flex flex-wrap items-center justify-between gap-4\">\n        <div className=\"flex items-center gap-3\">\n          <div className={`w-3 h-3 rounded-full ${statusColor}`} data-testid=\"indicator-server-status\" />\n          <h1 className=\"text-2xl font-bold\" data-testid=\"text-server-name\">{name}</h1>\n          <Badge variant=\"secondary\" data-testid=\"badge-server-status\">\n            {status.toUpperCase()}\n          </Badge>\n        </div>\n        \n        <div className=\"flex flex-wrap items-center gap-6 text-sm text-muted-foreground\">\n          <div className=\"flex items-center gap-2\" data-testid=\"info-map\">\n            <Globe className=\"w-4 h-4\" />\n            <span>{map}</span>\n          </div>\n          \n          <div className=\"flex items-center gap-2\" data-testid=\"info-players\">\n            <Users className=\"w-4 h-4\" />\n            <span>{playersCount}/{maxPlayers}</span>\n          </div>\n          \n          <div className=\"flex items-center gap-2\" data-testid=\"info-activity\">\n            <Activity className=\"w-4 h-4\" />\n            <span>Live</span>\n          </div>\n          \n          {lastUpdate && (\n            <div className=\"flex items-center gap-2\" data-testid=\"info-last-update\">\n              <Clock className=\"w-4 h-4\" />\n              <span>{lastUpdate}</span>\n            </div>\n          )}\n        </div>\n      </div>\n    </Card>\n  );\n}", "size_bytes": 2007}, "client/src/components/StatsCard.tsx": {"content": "import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { TrendingUp, TrendingDown, Minus } from \"lucide-react\";\n\ninterface StatsCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  trend?: \"up\" | \"down\" | \"stable\";\n  trendValue?: string;\n  icon?: React.ReactNode;\n  variant?: \"default\" | \"positive\" | \"negative\" | \"neutral\";\n}\n\nexport default function StatsCard({\n  title,\n  value,\n  subtitle,\n  trend,\n  trendValue,\n  icon,\n  variant = \"default\"\n}: StatsCardProps) {\n  const getTrendIcon = () => {\n    switch (trend) {\n      case \"up\": return <TrendingUp className=\"w-3 h-3\" />;\n      case \"down\": return <TrendingDown className=\"w-3 h-3\" />;\n      case \"stable\": return <Minus className=\"w-3 h-3\" />;\n      default: return null;\n    }\n  };\n\n  const getTrendColor = () => {\n    switch (trend) {\n      case \"up\": return \"text-green-600\";\n      case \"down\": return \"text-red-600\";\n      case \"stable\": return \"text-gray-500\";\n      default: return \"text-muted-foreground\";\n    }\n  };\n\n  const getVariantStyles = () => {\n    switch (variant) {\n      case \"positive\": return \"border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-900/20\";\n      case \"negative\": return \"border-red-200 bg-red-50/50 dark:border-red-800 dark:bg-red-900/20\";\n      case \"neutral\": return \"border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-900/20\";\n      default: return \"\";\n    }\n  };\n\n  return (\n    <Card data-testid={`card-stats-${title.toLowerCase().replace(/\\s+/g, '-')}`} className={`hover-elevate ${getVariantStyles()}`}>\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n        <CardTitle className=\"text-sm font-medium\" data-testid=\"text-stats-title\">\n          {title}\n        </CardTitle>\n        {icon && <div className=\"text-muted-foreground\">{icon}</div>}\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-2xl font-bold mb-1\" data-testid=\"text-stats-value\">\n          {value}\n        </div>\n        <div className=\"flex items-center justify-between\">\n          {subtitle && (\n            <p className=\"text-xs text-muted-foreground\" data-testid=\"text-stats-subtitle\">\n              {subtitle}\n            </p>\n          )}\n          {trend && trendValue && (\n            <div className={`flex items-center gap-1 text-xs ${getTrendColor()}`} data-testid=\"indicator-stats-trend\">\n              {getTrendIcon()}\n              <span>{trendValue}</span>\n            </div>\n          )}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}", "size_bytes": 2626}, "client/src/components/ThemeToggle.tsx": {"content": "import { Button } from \"@/components/ui/button\";\nimport { Moon, Sun } from \"lucide-react\";\nimport { useState, useEffect } from \"react\";\n\nexport default function ThemeToggle() {\n  const [theme, setTheme] = useState<\"light\" | \"dark\">(\"dark\");\n\n  useEffect(() => {\n    const savedTheme = localStorage.getItem(\"theme\") as \"light\" | \"dark\" | null;\n    if (savedTheme) {\n      setTheme(savedTheme);\n    } else {\n      setTheme(\"dark\"); // Default to dark theme for gaming dashboard\n    }\n  }, []);\n\n  useEffect(() => {\n    document.documentElement.classList.toggle(\"dark\", theme === \"dark\");\n    localStorage.setItem(\"theme\", theme);\n  }, [theme]);\n\n  const toggleTheme = () => {\n    setTheme(prev => prev === \"light\" ? \"dark\" : \"light\");\n  };\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={toggleTheme}\n      data-testid=\"button-theme-toggle\"\n      aria-label={`Switch to ${theme === \"light\" ? \"dark\" : \"light\"} mode`}\n    >\n      {theme === \"light\" ? (\n        <Moon className=\"h-4 w-4\" />\n      ) : (\n        <Sun className=\"h-4 w-4\" />\n      )}\n    </Button>\n  );\n}", "size_bytes": 1095}, "client/src/hooks/use-mobile.tsx": {"content": "import * as React from \"react\"\n\nconst MO<PERSON>LE_BREAKPOINT = 768\n\nexport function useIsMobile() {\n  const [isMobile, setIsMobile] = React.useState<boolean | undefined>(undefined)\n\n  React.useEffect(() => {\n    const mql = window.matchMedia(`(max-width: ${MOBILE_BREAKPOINT - 1}px)`)\n    const onChange = () => {\n      setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    }\n    mql.addEventListener(\"change\", onChange)\n    setIsMobile(window.innerWidth < MOBILE_BREAKPOINT)\n    return () => mql.removeEventListener(\"change\", onChange)\n  }, [])\n\n  return !!isMobile\n}\n", "size_bytes": 565}, "client/src/hooks/use-toast.ts": {"content": "import * as React from \"react\"\n\nimport type {\n  ToastActionElement,\n  ToastProps,\n} from \"@/components/ui/toast\"\n\nconst TOAST_LIMIT = 1\nconst TOAST_REMOVE_DELAY = 1000000\n\ntype ToasterToast = ToastProps & {\n  id: string\n  title?: React.ReactNode\n  description?: React.ReactNode\n  action?: ToastActionElement\n}\n\nconst actionTypes = {\n  ADD_TOAST: \"ADD_TOAST\",\n  UPDATE_TOAST: \"UPDATE_TOAST\",\n  DISMISS_TOAST: \"DISMISS_TOAST\",\n  REMOVE_TOAST: \"REMOVE_TOAST\",\n} as const\n\nlet count = 0\n\nfunction genId() {\n  count = (count + 1) % Number.MAX_SAFE_INTEGER\n  return count.toString()\n}\n\ntype ActionType = typeof actionTypes\n\ntype Action =\n  | {\n      type: ActionType[\"ADD_TOAST\"]\n      toast: ToasterToast\n    }\n  | {\n      type: ActionType[\"UPDATE_TOAST\"]\n      toast: Partial<ToasterToast>\n    }\n  | {\n      type: ActionType[\"DISMISS_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n  | {\n      type: ActionType[\"REMOVE_TOAST\"]\n      toastId?: ToasterToast[\"id\"]\n    }\n\ninterface State {\n  toasts: ToasterToast[]\n}\n\nconst toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>()\n\nconst addToRemoveQueue = (toastId: string) => {\n  if (toastTimeouts.has(toastId)) {\n    return\n  }\n\n  const timeout = setTimeout(() => {\n    toastTimeouts.delete(toastId)\n    dispatch({\n      type: \"REMOVE_TOAST\",\n      toastId: toastId,\n    })\n  }, TOAST_REMOVE_DELAY)\n\n  toastTimeouts.set(toastId, timeout)\n}\n\nexport const reducer = (state: State, action: Action): State => {\n  switch (action.type) {\n    case \"ADD_TOAST\":\n      return {\n        ...state,\n        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),\n      }\n\n    case \"UPDATE_TOAST\":\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === action.toast.id ? { ...t, ...action.toast } : t\n        ),\n      }\n\n    case \"DISMISS_TOAST\": {\n      const { toastId } = action\n\n      // ! Side effects ! - This could be extracted into a dismissToast() action,\n      // but I'll keep it here for simplicity\n      if (toastId) {\n        addToRemoveQueue(toastId)\n      } else {\n        state.toasts.forEach((toast) => {\n          addToRemoveQueue(toast.id)\n        })\n      }\n\n      return {\n        ...state,\n        toasts: state.toasts.map((t) =>\n          t.id === toastId || toastId === undefined\n            ? {\n                ...t,\n                open: false,\n              }\n            : t\n        ),\n      }\n    }\n    case \"REMOVE_TOAST\":\n      if (action.toastId === undefined) {\n        return {\n          ...state,\n          toasts: [],\n        }\n      }\n      return {\n        ...state,\n        toasts: state.toasts.filter((t) => t.id !== action.toastId),\n      }\n  }\n}\n\nconst listeners: Array<(state: State) => void> = []\n\nlet memoryState: State = { toasts: [] }\n\nfunction dispatch(action: Action) {\n  memoryState = reducer(memoryState, action)\n  listeners.forEach((listener) => {\n    listener(memoryState)\n  })\n}\n\ntype Toast = Omit<ToasterToast, \"id\">\n\nfunction toast({ ...props }: Toast) {\n  const id = genId()\n\n  const update = (props: ToasterToast) =>\n    dispatch({\n      type: \"UPDATE_TOAST\",\n      toast: { ...props, id },\n    })\n  const dismiss = () => dispatch({ type: \"DISMISS_TOAST\", toastId: id })\n\n  dispatch({\n    type: \"ADD_TOAST\",\n    toast: {\n      ...props,\n      id,\n      open: true,\n      onOpenChange: (open) => {\n        if (!open) dismiss()\n      },\n    },\n  })\n\n  return {\n    id: id,\n    dismiss,\n    update,\n  }\n}\n\nfunction useToast() {\n  const [state, setState] = React.useState<State>(memoryState)\n\n  React.useEffect(() => {\n    listeners.push(setState)\n    return () => {\n      const index = listeners.indexOf(setState)\n      if (index > -1) {\n        listeners.splice(index, 1)\n      }\n    }\n  }, [state])\n\n  return {\n    ...state,\n    toast,\n    dismiss: (toastId?: string) => dispatch({ type: \"DISMISS_TOAST\", toastId }),\n  }\n}\n\nexport { useToast, toast }\n", "size_bytes": 3895}, "client/src/hooks/useSocket.ts": {"content": "import { useEffect, useRef } from 'react';\nimport { io, Socket } from 'socket.io-client';\nimport { queryClient } from '@/lib/queryClient';\n\nexport function useSocket() {\n  const socketRef = useRef<Socket | null>(null);\n\n  useEffect(() => {\n    // Connect to Socket.IO server\n    const socket = io('/', {\n      transports: ['websocket', 'polling']\n    });\n\n    socketRef.current = socket;\n\n    // Handle connection events\n    socket.on('connect', () => {\n      console.log('Connected to WebSocket server:', socket.id);\n    });\n\n    socket.on('disconnect', () => {\n      console.log('Disconnected from WebSocket server');\n    });\n\n    // Handle real-time events\n    socket.on('newEvent', (event) => {\n      console.log('New event received:', event);\n      // Invalidate events query to refresh the kill feed\n      queryClient.invalidateQueries({ queryKey: ['/api/events'] });\n      \n      // Also invalidate dashboard summary for updated stats\n      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/summary'] });\n    });\n\n    socket.on('playerUpdate', (playerData) => {\n      console.log('Player update received:', playerData);\n      // Invalidate players query to refresh player list\n      queryClient.invalidateQueries({ queryKey: ['/api/players'] });\n      \n      // Invalidate server info for updated player count\n      queryClient.invalidateQueries({ queryKey: ['/api/server'] });\n    });\n\n    socket.on('statsUpdate', (statsData) => {\n      console.log('Stats update received:', statsData);\n      // Invalidate leaderboards to refresh rankings\n      queryClient.invalidateQueries({ queryKey: ['/api/leaderboard'] });\n      \n      // Invalidate dashboard summary for updated stats\n      queryClient.invalidateQueries({ queryKey: ['/api/dashboard/summary'] });\n    });\n\n    // Cleanup on unmount\n    return () => {\n      socket.disconnect();\n    };\n  }, []);\n\n  return socketRef.current;\n}", "size_bytes": 1901}, "client/src/lib/queryClient.ts": {"content": "import { QueryClient, QueryFunction } from \"@tanstack/react-query\";\n\nasync function throwIfResNotOk(res: Response) {\n  if (!res.ok) {\n    const text = (await res.text()) || res.statusText;\n    throw new Error(`${res.status}: ${text}`);\n  }\n}\n\nexport async function apiRequest(\n  method: string,\n  url: string,\n  data?: unknown | undefined,\n): Promise<Response> {\n  const res = await fetch(url, {\n    method,\n    headers: data ? { \"Content-Type\": \"application/json\" } : {},\n    body: data ? JSON.stringify(data) : undefined,\n    credentials: \"include\",\n  });\n\n  await throwIfResNotOk(res);\n  return res;\n}\n\ntype UnauthorizedBehavior = \"returnNull\" | \"throw\";\nexport const getQueryFn: <T>(options: {\n  on401: UnauthorizedBehavior;\n}) => QueryFunction<T> =\n  ({ on401: unauthorizedBehavior }) =>\n  async ({ queryKey }) => {\n    const res = await fetch(queryKey.join(\"/\") as string, {\n      credentials: \"include\",\n    });\n\n    if (unauthorizedBehavior === \"returnNull\" && res.status === 401) {\n      return null;\n    }\n\n    await throwIfResNotOk(res);\n    return await res.json();\n  };\n\nexport const queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      queryFn: getQueryFn({ on401: \"throw\" }),\n      refetchInterval: false,\n      refetchOnWindowFocus: false,\n      staleTime: 30 * 1000, // 30 seconds instead of Infinity\n      retry: false,\n    },\n    mutations: {\n      retry: false,\n    },\n  },\n});\n", "size_bytes": 1418}, "client/src/lib/utils.ts": {"content": "import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n", "size_bytes": 166}, "client/src/pages/Dashboard.tsx": {"content": "import { useState } from \"react\";\nimport { useQuery, useMutation } from \"@tanstack/react-query\";\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from \"@/components/ui/tabs\";\nimport { Input } from \"@/components/ui/input\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Search, Filter, RefreshCw } from \"lucide-react\";\nimport { queryClient, apiRequest } from \"@/lib/queryClient\";\nimport { useSocket } from \"@/hooks/useSocket\";\n\nimport ServerHeader from \"@/components/ServerHeader\";\nimport StatsCard from \"@/components/StatsCard\";\nimport PlayerCard from \"@/components/PlayerCard\";\nimport Leaderboard from \"@/components/Leaderboard\";\nimport KillFeed from \"@/components/KillFeed\";\n\n// Type definitions for API responses\ninterface Player {\n  steamId: string;\n  name: string;\n  eosId?: string;\n  kills: number;\n  deaths: number;\n  revives: number;\n  status: \"online\" | \"offline\";\n  lastSeen: string;\n  squad?: string;\n  role?: string;\n}\n\ninterface ServerInfo {\n  name: string;\n  currentMap: string;\n  playerCount: number;\n  maxPlayers: number;\n  status: string;\n  lastUpdate: string;\n  uptime?: string;\n}\n\ninterface LeaderboardEntry {\n  rank: number;\n  steamId: string;\n  name: string;\n  value: number;\n}\n\ninterface Leaderboard {\n  metric: string;\n  period: string;\n  entries: LeaderboardEntry[];\n  lastUpdated: string;\n}\n\ninterface DashboardSummary {\n  activePlayers: number;\n  maxPlayers: number;\n  totalKills24h: number;\n  totalRevives24h: number;\n  serverUptime: string;\n  topKillers: LeaderboardEntry[];\n  topMedics: LeaderboardEntry[];\n  recentEvents: any[];\n}\n\ninterface GameEvent {\n  id: string | number;\n  type: string;\n  timestamp: string;\n  actor?: string;\n  target?: string;\n  meta?: {\n    weapon?: string;\n    headshot?: boolean;\n    [key: string]: any;\n  };\n}\n\ninterface KillFeedEvent {\n  id: string;\n  type: \"kill\" | \"death\" | \"revive\" | \"join\" | \"leave\";\n  timestamp: string;\n  actor?: string;\n  target?: string;\n  weapon?: string;\n  headshot?: boolean;\n}\n\nexport default function Dashboard() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  \n  // Initialize WebSocket connection for real-time updates\n  useSocket();\n\n  // Fetch server information\n  const { data: serverInfo, isLoading: serverLoading } = useQuery<ServerInfo>({\n    queryKey: [\"/api/server\"],\n    refetchInterval: 30000, // Refresh every 30 seconds\n  });\n\n  // Fetch players list\n  const { data: players = [], isLoading: playersLoading, refetch: refetchPlayers } = useQuery<Player[]>({\n    queryKey: [\"/api/players\"],\n    refetchInterval: 15000, // Refresh every 15 seconds\n  });\n\n  // Fetch leaderboards\n  const { data: killsLeaderboard, isLoading: killsLoading } = useQuery<Leaderboard>({\n    queryKey: [\"/api/leaderboard/kills/week\"],\n    refetchInterval: 60000, // Refresh every minute\n  });\n\n  const { data: revivesLeaderboard, isLoading: revivesLoading } = useQuery<Leaderboard>({\n    queryKey: [\"/api/leaderboard/revives/week\"],\n    refetchInterval: 60000, // Refresh every minute\n  });\n\n  // Fetch recent events\n  const { data: events = [], isLoading: eventsLoading, refetch: refetchEvents } = useQuery<GameEvent[]>({\n    queryKey: [\"/api/events\"],\n    refetchInterval: 10000, // Refresh every 10 seconds\n  });\n\n  // Dashboard summary for stats cards\n  const { data: dashboardSummary, isLoading: summaryLoading, refetch: refetchSummary } = useQuery<DashboardSummary>({\n    queryKey: [\"/api/dashboard/summary\"],\n    refetchInterval: 30000, // Refresh every 30 seconds\n  });\n\n  // Filter players based on search term\n  const filteredPlayers = players.filter((player) =>\n    player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n    player.steamId.includes(searchTerm)\n  );\n\n  // Refresh all data mutation\n  const refreshMutation = useMutation({\n    mutationFn: async () => {\n      // Invalidate all queries to force refresh\n      await queryClient.invalidateQueries({ queryKey: [\"/api/server\"] });\n      await queryClient.invalidateQueries({ queryKey: [\"/api/players\"] });\n      await queryClient.invalidateQueries({ queryKey: [\"/api/leaderboard\"] });\n      await queryClient.invalidateQueries({ queryKey: [\"/api/events\"] });\n      await queryClient.invalidateQueries({ queryKey: [\"/api/dashboard/summary\"] });\n      return \"refreshed\";\n    },\n    onSuccess: () => {\n      console.log('Dashboard refreshed successfully');\n    }\n  });\n\n  const handleRefresh = () => {\n    refreshMutation.mutate();\n  };\n\n  const handlePlayerSteamLink = (steamId: string) => {\n    console.log(`Opening Steam profile for ${steamId}`);\n    // todo: implement Steam profile opening\n  };\n\n  const handlePlayerStats = (steamId: string) => {\n    console.log(`Viewing detailed stats for ${steamId}`);\n    // todo: implement detailed stats modal\n  };\n\n  return (\n    <div className=\"space-y-6 p-6\">\n      {/* Server Header */}\n      <div className=\"flex items-center justify-between\">\n        <ServerHeader\n          name={serverInfo?.name || \"Squad Server\"}\n          map={serverInfo?.currentMap || \"Unknown Map\"}\n          playersCount={serverInfo?.playerCount || 0}\n          maxPlayers={serverInfo?.maxPlayers || 96}\n          status={(serverInfo?.status === \"online\" || serverInfo?.status === \"offline\") ? serverInfo.status : \"offline\"}\n          lastUpdate={serverInfo?.lastUpdate ? new Date(serverInfo.lastUpdate).toLocaleTimeString() : \"Unknown\"}\n        />\n        <Button\n          variant=\"outline\"\n          size=\"icon\"\n          onClick={handleRefresh}\n          data-testid=\"button-refresh-dashboard\"\n        >\n          <RefreshCw className=\"w-4 h-4\" />\n        </Button>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n        <StatsCard\n          title=\"Active Players\"\n          value={dashboardSummary?.activePlayers || 0}\n          subtitle={`of ${dashboardSummary?.maxPlayers || 96} max`}\n          trend=\"up\"\n          trendValue=\"+5\"\n          variant=\"positive\"\n        />\n        <StatsCard\n          title=\"Total Kills\"\n          value={dashboardSummary?.totalKills24h || 0}\n          subtitle=\"last 24h\" \n          trend=\"stable\"\n          trendValue=\"0%\"\n        />\n        <StatsCard\n          title=\"Revives\"\n          value={dashboardSummary?.totalRevives24h || 0}\n          subtitle=\"last 24h\"\n          trend=\"up\"\n          trendValue=\"+12%\"\n          variant=\"positive\"\n        />\n        <StatsCard\n          title=\"Server Uptime\"\n          value={dashboardSummary?.serverUptime || \"98.5%\"}\n          subtitle=\"last 30 days\"\n          trend=\"down\"\n          trendValue=\"-0.3%\"\n          variant=\"neutral\"\n        />\n      </div>\n\n      {/* Main Content Tabs */}\n      <Tabs defaultValue=\"players\" className=\"space-y-4\">\n        <TabsList data-testid=\"tabs-dashboard-main\">\n          <TabsTrigger value=\"players\" data-testid=\"tab-players\">Players</TabsTrigger>\n          <TabsTrigger value=\"leaderboards\" data-testid=\"tab-leaderboards\">Leaderboards</TabsTrigger>\n          <TabsTrigger value=\"events\" data-testid=\"tab-events\">Live Events</TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"players\" className=\"space-y-4\">\n          <div className=\"flex items-center gap-4\">\n            <div className=\"relative flex-1 max-w-md\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground\" />\n              <Input\n                placeholder=\"Search players by name or Steam ID...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"pl-10\"\n                data-testid=\"input-player-search\"\n              />\n            </div>\n            <Button variant=\"outline\" data-testid=\"button-filter-players\">\n              <Filter className=\"w-4 h-4 mr-2\" />\n              Filter\n            </Button>\n          </div>\n\n          {playersLoading ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              Loading players...\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {filteredPlayers.map((player) => (\n                <PlayerCard\n                  key={player.steamId}\n                  name={player.name}\n                  steamId={player.steamId}\n                  squad={player.squad || \"Unknown\"}\n                  role={player.role || \"Unknown\"}\n                  score={player.kills * 10} // Calculate score based on kills for now\n                  kills={player.kills}\n                  deaths={player.deaths}\n                  revives={player.revives}\n                  status={player.status}\n                  onSteamLink={() => handlePlayerSteamLink(player.steamId)}\n                  onViewStats={() => handlePlayerStats(player.steamId)}\n                />\n              ))}\n              {filteredPlayers.length === 0 && !playersLoading && (\n                <div className=\"text-center py-8 text-muted-foreground\">\n                  No players found matching your search.\n                </div>\n              )}\n            </div>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"leaderboards\" className=\"space-y-6\">\n          {killsLoading || revivesLoading ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              Loading leaderboards...\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <Leaderboard\n                title=\"Top Killers\"\n                entries={killsLeaderboard?.entries || []}\n                metric=\"kills\"\n                period=\"week\"\n              />\n              <Leaderboard\n                title=\"Best Medics\"\n                entries={revivesLeaderboard?.entries || []}\n                metric=\"revives\"\n                period=\"week\"\n              />\n            </div>\n          )}\n        </TabsContent>\n\n        <TabsContent value=\"events\">\n          {eventsLoading ? (\n            <div className=\"text-center py-8 text-muted-foreground\">\n              Loading events...\n            </div>\n          ) : (\n            <KillFeed events={events.map(event => ({\n              id: String(event.id),\n              type: event.type as \"kill\" | \"death\" | \"revive\" | \"join\" | \"leave\",\n              timestamp: event.timestamp,\n              actor: event.actor,\n              target: event.target,\n              weapon: event.meta?.weapon,\n              headshot: event.meta?.headshot\n            }))} />\n          )}\n        </TabsContent>\n      </Tabs>\n    </div>\n  );\n}", "size_bytes": 10516}, "client/src/pages/not-found.tsx": {"content": "import { Card, CardContent } from \"@/components/ui/card\";\nimport { AlertCircle } from \"lucide-react\";\n\nexport default function NotFound() {\n  return (\n    <div className=\"min-h-screen w-full flex items-center justify-center bg-gray-50\">\n      <Card className=\"w-full max-w-md mx-4\">\n        <CardContent className=\"pt-6\">\n          <div className=\"flex mb-4 gap-2\">\n            <AlertCircle className=\"h-8 w-8 text-red-500\" />\n            <h1 className=\"text-2xl font-bold text-gray-900\">404 Page Not Found</h1>\n          </div>\n\n          <p className=\"mt-4 text-sm text-gray-600\">\n            Did you forget to add the page to the router?\n          </p>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n", "size_bytes": 711}, "client/src/components/examples/AppSidebar.tsx": {"content": "import { AppSidebar } from '../AppSidebar';\nimport { SidebarProvider } from \"@/components/ui/sidebar\";\n\nexport default function AppSidebarExample() {\n  const style = {\n    \"--sidebar-width\": \"20rem\",\n    \"--sidebar-width-icon\": \"4rem\",\n  };\n\n  return (\n    <SidebarProvider style={style as React.CSSProperties}>\n      <div className=\"flex h-screen w-full\">\n        <AppSidebar />\n        <div className=\"flex-1 p-6\">\n          <div className=\"rounded-lg border-2 border-dashed border-muted-foreground/25 p-8 text-center\">\n            <p className=\"text-muted-foreground\">\n              Main content area - sidebar navigation demo\n            </p>\n          </div>\n        </div>\n      </div>\n    </SidebarProvider>\n  );\n}", "size_bytes": 721}, "client/src/components/examples/KillFeed.tsx": {"content": "import KillFeed from '../KillFeed';\n\nexport default function KillFeedExample() {\n  // Example component - uses empty events array since real data comes from API\n  const events = [];\n\n  return <KillFeed events={events} />;\n}", "size_bytes": 223}, "client/src/components/examples/Leaderboard.tsx": {"content": "import Leaderboard from '../Leaderboard';\n\nexport default function LeaderboardExample() {\n  const killsLeaderboard = [\n    { rank: 1, name: \"<PERSON><PERSON>\", steamId: \"76561199585954249\", value: 42 },\n    { rank: 2, name: \"<PERSON><PERSON><PERSON><PERSON>\", steamId: \"76561198123456789\", value: 38 },\n    { rank: 3, name: \"<PERSON><PERSON>\", steamId: \"76561198987654321\", value: 35 },\n    { rank: 4, name: \"<PERSON>niper<PERSON><PERSON>\", steamId: \"76561198111222333\", value: 32 },\n    { rank: 5, name: \"Medics<PERSON><PERSON>\", steamId: \"76561198444555666\", value: 28 }\n  ];\n\n  const kdLeaderboard = [\n    { rank: 1, name: \"<PERSON>Sni<PERSON>\", steamId: \"76561198777888999\", value: 3.45 },\n    { rank: 2, name: \"PrecisionKing\", steamId: \"76561198000111222\", value: 2.89 },\n    { rank: 3, name: \"<PERSON><PERSON><PERSON>\", steamId: \"76561198333444555\", value: 2.67 },\n    { rank: 4, name: \"<PERSON><PERSON>\", steamId: \"76561198666777888\", value: 2.34 },\n    { rank: 5, name: \"SteadyHand\", steamId: \"76561198999000111\", value: 2.12 }\n  ];\n\n  return (\n    <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n      <Leaderboard\n        title=\"Top Killers\"\n        entries={killsLeaderboard}\n        metric=\"kills\"\n        period=\"week\"\n      />\n      <Leaderboard\n        title=\"Best K/D Ratio\"\n        entries={kdLeaderboard}\n        metric=\"K/D\"\n        period=\"month\"\n      />\n    </div>\n  );\n}", "size_bytes": 1297}, "client/src/components/examples/PlayerCard.tsx": {"content": "import PlayerCard from '../PlayerCard';\n\nexport default function PlayerCardExample() {\n  return (\n    <div className=\"space-y-4\">\n      <PlayerCard\n        name=\"KING\"\n        steamId=\"76561199585954249\"\n        squad=\"Alpha-1\"\n        role=\"Rifleman\"\n        score={1200}\n        kills={15}\n        deaths={8}\n        revives={5}\n        status=\"online\"\n        onSteamLink={() => console.log('Steam link clicked')}\n        onViewStats={() => console.log('View stats clicked')}\n      />\n      <PlayerCard\n        name=\"GoldenEagle\"\n        steamId=\"76561198123456789\"\n        squad=\"Bravo-2\"\n        role=\"Medic\"\n        score={950}\n        kills={8}\n        deaths={12}\n        revives={18}\n        status=\"offline\"\n        onSteamLink={() => console.log('Steam link clicked')}\n        onViewStats={() => console.log('View stats clicked')}\n      />\n    </div>\n  );\n}", "size_bytes": 868}, "client/src/components/examples/ServerHeader.tsx": {"content": "import ServerHeader from '../ServerHeader';\n\nexport default function ServerHeaderExample() {\n  return (\n    <ServerHeader\n      name=\"My Squad Server\"\n      map=\"GE AlBasrah Invasion v7\"\n      playersCount={42}\n      maxPlayers={96}\n      status=\"online\"\n      lastUpdate=\"30s ago\"\n    />\n  );\n}", "size_bytes": 295}, "client/src/components/examples/StatsCard.tsx": {"content": "import StatsCard from '../StatsCard';\nimport { <PERSON>, <PERSON>, Heart, Target } from \"lucide-react\";\n\nexport default function StatsCardExample() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n      <StatsCard\n        title=\"Active Players\"\n        value={42}\n        subtitle=\"of 96 max\"\n        trend=\"up\"\n        trendValue=\"+5\"\n        icon={<Users className=\"w-4 h-4\" />}\n        variant=\"positive\"\n      />\n      <StatsCard\n        title=\"Total Kills\"\n        value=\"1,248\"\n        subtitle=\"last 24h\"\n        trend=\"stable\"\n        trendValue=\"0%\"\n        icon={<Skull className=\"w-4 h-4\" />}\n      />\n      <StatsCard\n        title=\"Revives\"\n        value={345}\n        subtitle=\"last 24h\"\n        trend=\"up\"\n        trendValue=\"+12%\"\n        icon={<Heart className=\"w-4 h-4\" />}\n        variant=\"positive\"\n      />\n      <StatsCard\n        title=\"Server Uptime\"\n        value=\"98.5%\"\n        subtitle=\"last 30 days\"\n        trend=\"down\"\n        trendValue=\"-0.3%\"\n        icon={<Target className=\"w-4 h-4\" />}\n        variant=\"neutral\"\n      />\n    </div>\n  );\n}", "size_bytes": 1108}, "client/src/components/examples/ThemeToggle.tsx": {"content": "import ThemeToggle from '../ThemeToggle';\n\nexport default function ThemeToggleExample() {\n  return (\n    <div className=\"flex items-center gap-4 p-4\">\n      <span className=\"text-sm text-muted-foreground\">Toggle theme:</span>\n      <ThemeToggle />\n    </div>\n  );\n}", "size_bytes": 265}, "client/src/components/ui/accordion.tsx": {"content": "import * as React from \"react\"\nimport * as AccordionPrimitive from \"@radix-ui/react-accordion\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Accordion = AccordionPrimitive.Root\n\nconst AccordionItem = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <AccordionPrimitive.Item\n    ref={ref}\n    className={cn(\"border-b\", className)}\n    {...props}\n  />\n))\nAccordionItem.displayName = \"AccordionItem\"\n\nconst AccordionTrigger = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Header className=\"flex\">\n    <AccordionPrimitive.Trigger\n      ref={ref}\n      className={cn(\n        \"flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline [&[data-state=open]>svg]:rotate-180\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronDown className=\"h-4 w-4 shrink-0 transition-transform duration-200\" />\n    </AccordionPrimitive.Trigger>\n  </AccordionPrimitive.Header>\n))\nAccordionTrigger.displayName = AccordionPrimitive.Trigger.displayName\n\nconst AccordionContent = React.forwardRef<\n  React.ElementRef<typeof AccordionPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AccordionPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <AccordionPrimitive.Content\n    ref={ref}\n    className=\"overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down\"\n    {...props}\n  >\n    <div className={cn(\"pb-4 pt-0\", className)}>{children}</div>\n  </AccordionPrimitive.Content>\n))\n\nAccordionContent.displayName = AccordionPrimitive.Content.displayName\n\nexport { Accordion, AccordionItem, AccordionTrigger, AccordionContent }\n", "size_bytes": 1977}, "client/src/components/ui/alert-dialog.tsx": {"content": "import * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n", "size_bytes": 4420}, "client/src/components/ui/alert.tsx": {"content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n", "size_bytes": 1584}, "client/src/components/ui/aspect-ratio.tsx": {"content": "import * as AspectRatioPrimitive from \"@radix-ui/react-aspect-ratio\"\n\nconst AspectRatio = AspectRatioPrimitive.Root\n\nexport { AspectRatio }\n", "size_bytes": 140}, "client/src/components/ui/avatar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(`\n      after:content-[''] after:block after:absolute after:inset-0 after:rounded-full after:pointer-events-none after:border after:border-black/10 dark:after:border-white/10\n      relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full`,\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n", "size_bytes": 1592}, "client/src/components/ui/badge.tsx": {"content": "import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  // Whitespace-nowrap: Badges should never wrap.\n  \"whitespace-nowrap inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\" +\n  \" hover-elevate \" ,\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground shadow-xs\",\n        secondary: \"border-transparent bg-secondary text-secondary-foreground\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground shadow-xs\",\n\n        outline: \" border [border-color:var(--badge-outline)] shadow-xs\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  },\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants }\n", "size_bytes": 1202}, "client/src/components/ui/breadcrumb.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Breadcrumb = React.forwardRef<\n  HTMLElement,\n  React.ComponentPropsWithoutRef<\"nav\"> & {\n    separator?: React.ReactNode\n  }\n>(({ ...props }, ref) => <nav ref={ref} aria-label=\"breadcrumb\" {...props} />)\nBreadcrumb.displayName = \"Breadcrumb\"\n\nconst BreadcrumbList = React.forwardRef<\n  HTMLOListElement,\n  React.ComponentPropsWithoutRef<\"ol\">\n>(({ className, ...props }, ref) => (\n  <ol\n    ref={ref}\n    className={cn(\n      \"flex flex-wrap items-center gap-1.5 break-words text-sm text-muted-foreground sm:gap-2.5\",\n      className\n    )}\n    {...props}\n  />\n))\nBreadcrumbList.displayName = \"BreadcrumbList\"\n\nconst BreadcrumbItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentPropsWithoutRef<\"li\">\n>(({ className, ...props }, ref) => (\n  <li\n    ref={ref}\n    className={cn(\"inline-flex items-center gap-1.5\", className)}\n    {...props}\n  />\n))\nBreadcrumbItem.displayName = \"BreadcrumbItem\"\n\nconst BreadcrumbLink = React.forwardRef<\n  HTMLAnchorElement,\n  React.ComponentPropsWithoutRef<\"a\"> & {\n    asChild?: boolean\n  }\n>(({ asChild, className, ...props }, ref) => {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      ref={ref}\n      className={cn(\"transition-colors hover:text-foreground\", className)}\n      {...props}\n    />\n  )\n})\nBreadcrumbLink.displayName = \"BreadcrumbLink\"\n\nconst BreadcrumbPage = React.forwardRef<\n  HTMLSpanElement,\n  React.ComponentPropsWithoutRef<\"span\">\n>(({ className, ...props }, ref) => (\n  <span\n    ref={ref}\n    role=\"link\"\n    aria-disabled=\"true\"\n    aria-current=\"page\"\n    className={cn(\"font-normal text-foreground\", className)}\n    {...props}\n  />\n))\nBreadcrumbPage.displayName = \"BreadcrumbPage\"\n\nconst BreadcrumbSeparator = ({\n  children,\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) => (\n  <li\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"[&>svg]:w-3.5 [&>svg]:h-3.5\", className)}\n    {...props}\n  >\n    {children ?? <ChevronRight />}\n  </li>\n)\nBreadcrumbSeparator.displayName = \"BreadcrumbSeparator\"\n\nconst BreadcrumbEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    role=\"presentation\"\n    aria-hidden=\"true\"\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More</span>\n  </span>\n)\nBreadcrumbEllipsis.displayName = \"BreadcrumbElipssis\"\n\nexport {\n  Breadcrumb,\n  BreadcrumbList,\n  BreadcrumbItem,\n  BreadcrumbLink,\n  BreadcrumbPage,\n  BreadcrumbSeparator,\n  BreadcrumbEllipsis,\n}\n", "size_bytes": 2712}, "client/src/components/ui/button.tsx": {"content": "import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\" +\n  \" hover-elevate active-elevate-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground border border-primary-border\",\n        destructive:\n          \"bg-destructive text-destructive-foreground border border-destructive-border\",\n        outline:\n          // Shows the background color of whatever card / sidebar / accent background it is inside of.\n          // Inherits the current text color.\n          \" border [border-color:var(--button-outline)]  shadow-xs active:shadow-none \",\n        secondary: \"border bg-secondary text-secondary-foreground border border-secondary-border \",\n        // Add a transparent border so that when someone toggles a border on later, it doesn't shift layout/size.\n        ghost: \"border border-transparent\",\n      },\n      // Heights are set as \"min\" heights, because sometimes Ai will place large amount of content\n      // inside buttons. With a min-height they will look appropriate with small amounts of content,\n      // but will expand to fit large amounts of content.\n      size: {\n        default: \"min-h-9 px-4 py-2\",\n        sm: \"min-h-8 rounded-md px-3 text-xs\",\n        lg: \"min-h-10 rounded-md px-8\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n", "size_bytes": 2359}, "client/src/components/ui/calendar.tsx": {"content": "import * as React from \"react\"\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\nimport { DayPicker } from \"react-day-picker\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\n\nfunction Calendar({\n  className,\n  classNames,\n  showOutsideDays = true,\n  ...props\n}: CalendarProps) {\n  return (\n    <DayPicker\n      showOutsideDays={showOutsideDays}\n      className={cn(\"p-3\", className)}\n      classNames={{\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\n        month: \"space-y-4\",\n        caption: \"flex justify-center pt-1 relative items-center\",\n        caption_label: \"text-sm font-medium\",\n        nav: \"space-x-1 flex items-center\",\n        nav_button: cn(\n          buttonVariants({ variant: \"outline\" }),\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\n        ),\n        nav_button_previous: \"absolute left-1\",\n        nav_button_next: \"absolute right-1\",\n        table: \"w-full border-collapse space-y-1\",\n        head_row: \"flex\",\n        head_cell:\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\n        row: \"flex w-full mt-2\",\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\n        day: cn(\n          buttonVariants({ variant: \"ghost\" }),\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\n        ),\n        day_range_end: \"day-range-end\",\n        day_selected:\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\n        day_today: \"bg-accent text-accent-foreground\",\n        day_outside:\n          \"day-outside text-muted-foreground aria-selected:bg-accent/50 aria-selected:text-muted-foreground\",\n        day_disabled: \"text-muted-foreground opacity-50\",\n        day_range_middle:\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\n        day_hidden: \"invisible\",\n        ...classNames,\n      }}\n      components={{\n        IconLeft: ({ className, ...props }) => (\n          <ChevronLeft className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n        IconRight: ({ className, ...props }) => (\n          <ChevronRight className={cn(\"h-4 w-4\", className)} {...props} />\n        ),\n      }}\n      {...props}\n    />\n  )\n}\nCalendar.displayName = \"Calendar\"\n\nexport { Calendar }\n", "size_bytes": 2695}, "client/src/components/ui/card.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"shadcn-card rounded-xl border bg-card border-card-border text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n))\nCardFooter.displayName = \"CardFooter\"\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardDescription,\n  CardContent,\n}\n", "size_bytes": 1904}, "client/src/components/ui/carousel.tsx": {"content": "import * as React from \"react\"\nimport useEmblaCarousel, {\n  type UseEmblaCarouselType,\n} from \"embla-carousel-react\"\nimport { ArrowLeft, ArrowRight } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\n\ntype CarouselApi = UseEmblaCarouselType[1]\ntype UseCarouselParameters = Parameters<typeof useEmblaCarousel>\ntype CarouselOptions = UseCarouselParameters[0]\ntype CarouselPlugin = UseCarouselParameters[1]\n\ntype CarouselProps = {\n  opts?: CarouselOptions\n  plugins?: CarouselPlugin\n  orientation?: \"horizontal\" | \"vertical\"\n  setApi?: (api: CarouselApi) => void\n}\n\ntype CarouselContextProps = {\n  carouselRef: ReturnType<typeof useEmblaCarousel>[0]\n  api: ReturnType<typeof useEmblaCarousel>[1]\n  scrollPrev: () => void\n  scrollNext: () => void\n  canScrollPrev: boolean\n  canScrollNext: boolean\n} & CarouselProps\n\nconst CarouselContext = React.createContext<CarouselContextProps | null>(null)\n\nfunction useCarousel() {\n  const context = React.useContext(CarouselContext)\n\n  if (!context) {\n    throw new Error(\"useCarousel must be used within a <Carousel />\")\n  }\n\n  return context\n}\n\nconst Carousel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & CarouselProps\n>(\n  (\n    {\n      orientation = \"horizontal\",\n      opts,\n      setApi,\n      plugins,\n      className,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const [carouselRef, api] = useEmblaCarousel(\n      {\n        ...opts,\n        axis: orientation === \"horizontal\" ? \"x\" : \"y\",\n      },\n      plugins\n    )\n    const [canScrollPrev, setCanScrollPrev] = React.useState(false)\n    const [canScrollNext, setCanScrollNext] = React.useState(false)\n\n    const onSelect = React.useCallback((api: CarouselApi) => {\n      if (!api) {\n        return\n      }\n\n      setCanScrollPrev(api.canScrollPrev())\n      setCanScrollNext(api.canScrollNext())\n    }, [])\n\n    const scrollPrev = React.useCallback(() => {\n      api?.scrollPrev()\n    }, [api])\n\n    const scrollNext = React.useCallback(() => {\n      api?.scrollNext()\n    }, [api])\n\n    const handleKeyDown = React.useCallback(\n      (event: React.KeyboardEvent<HTMLDivElement>) => {\n        if (event.key === \"ArrowLeft\") {\n          event.preventDefault()\n          scrollPrev()\n        } else if (event.key === \"ArrowRight\") {\n          event.preventDefault()\n          scrollNext()\n        }\n      },\n      [scrollPrev, scrollNext]\n    )\n\n    React.useEffect(() => {\n      if (!api || !setApi) {\n        return\n      }\n\n      setApi(api)\n    }, [api, setApi])\n\n    React.useEffect(() => {\n      if (!api) {\n        return\n      }\n\n      onSelect(api)\n      api.on(\"reInit\", onSelect)\n      api.on(\"select\", onSelect)\n\n      return () => {\n        api?.off(\"select\", onSelect)\n      }\n    }, [api, onSelect])\n\n    return (\n      <CarouselContext.Provider\n        value={{\n          carouselRef,\n          api: api,\n          opts,\n          orientation:\n            orientation || (opts?.axis === \"y\" ? \"vertical\" : \"horizontal\"),\n          scrollPrev,\n          scrollNext,\n          canScrollPrev,\n          canScrollNext,\n        }}\n      >\n        <div\n          ref={ref}\n          onKeyDownCapture={handleKeyDown}\n          className={cn(\"relative\", className)}\n          role=\"region\"\n          aria-roledescription=\"carousel\"\n          {...props}\n        >\n          {children}\n        </div>\n      </CarouselContext.Provider>\n    )\n  }\n)\nCarousel.displayName = \"Carousel\"\n\nconst CarouselContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const { carouselRef, orientation } = useCarousel()\n\n  return (\n    <div ref={carouselRef} className=\"overflow-hidden\">\n      <div\n        ref={ref}\n        className={cn(\n          \"flex\",\n          orientation === \"horizontal\" ? \"-ml-4\" : \"-mt-4 flex-col\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n})\nCarouselContent.displayName = \"CarouselContent\"\n\nconst CarouselItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const { orientation } = useCarousel()\n\n  return (\n    <div\n      ref={ref}\n      role=\"group\"\n      aria-roledescription=\"slide\"\n      className={cn(\n        \"min-w-0 shrink-0 grow-0 basis-full\",\n        orientation === \"horizontal\" ? \"pl-4\" : \"pt-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n})\nCarouselItem.displayName = \"CarouselItem\"\n\nconst CarouselPrevious = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<typeof Button>\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\n  const { orientation, scrollPrev, canScrollPrev } = useCarousel()\n\n  return (\n    <Button\n      ref={ref}\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute  h-8 w-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"-left-12 top-1/2 -translate-y-1/2\"\n          : \"-top-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollPrev}\n      onClick={scrollPrev}\n      {...props}\n    >\n      <ArrowLeft className=\"h-4 w-4\" />\n      <span className=\"sr-only\">Previous slide</span>\n    </Button>\n  )\n})\nCarouselPrevious.displayName = \"CarouselPrevious\"\n\nconst CarouselNext = React.forwardRef<\n  HTMLButtonElement,\n  React.ComponentProps<typeof Button>\n>(({ className, variant = \"outline\", size = \"icon\", ...props }, ref) => {\n  const { orientation, scrollNext, canScrollNext } = useCarousel()\n\n  return (\n    <Button\n      ref={ref}\n      variant={variant}\n      size={size}\n      className={cn(\n        \"absolute h-8 w-8 rounded-full\",\n        orientation === \"horizontal\"\n          ? \"-right-12 top-1/2 -translate-y-1/2\"\n          : \"-bottom-12 left-1/2 -translate-x-1/2 rotate-90\",\n        className\n      )}\n      disabled={!canScrollNext}\n      onClick={scrollNext}\n      {...props}\n    >\n      <ArrowRight className=\"h-4 w-4\" />\n      <span className=\"sr-only\">Next slide</span>\n    </Button>\n  )\n})\nCarouselNext.displayName = \"CarouselNext\"\n\nexport {\n  type CarouselApi,\n  Carousel,\n  CarouselContent,\n  CarouselItem,\n  CarouselPrevious,\n  CarouselNext,\n}\n", "size_bytes": 6210}, "client/src/components/ui/chart.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as RechartsPrimitive from \"recharts\"\n\nimport { cn } from \"@/lib/utils\"\n\n// Format: { THEME_NAME: CSS_SELECTOR }\nconst THEMES = { light: \"\", dark: \".dark\" } as const\n\nexport type ChartConfig = {\n  [k in string]: {\n    label?: React.ReactNode\n    icon?: React.ComponentType\n  } & (\n    | { color?: string; theme?: never }\n    | { color?: never; theme: Record<keyof typeof THEMES, string> }\n  )\n}\n\ntype ChartContextProps = {\n  config: ChartConfig\n}\n\nconst ChartContext = React.createContext<ChartContextProps | null>(null)\n\nfunction useChart() {\n  const context = React.useContext(ChartContext)\n\n  if (!context) {\n    throw new Error(\"useChart must be used within a <ChartContainer />\")\n  }\n\n  return context\n}\n\nconst ChartContainer = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> & {\n    config: ChartConfig\n    children: React.ComponentProps<\n      typeof RechartsPrimitive.ResponsiveContainer\n    >[\"children\"]\n  }\n>(({ id, className, children, config, ...props }, ref) => {\n  const uniqueId = React.useId()\n  const chartId = `chart-${id || uniqueId.replace(/:/g, \"\")}`\n\n  return (\n    <ChartContext.Provider value={{ config }}>\n      <div\n        data-chart={chartId}\n        ref={ref}\n        className={cn(\n          \"flex aspect-video justify-center text-xs [&_.recharts-cartesian-axis-tick_text]:fill-muted-foreground [&_.recharts-cartesian-grid_line[stroke='#ccc']]:stroke-border/50 [&_.recharts-curve.recharts-tooltip-cursor]:stroke-border [&_.recharts-dot[stroke='#fff']]:stroke-transparent [&_.recharts-layer]:outline-none [&_.recharts-polar-grid_[stroke='#ccc']]:stroke-border [&_.recharts-radial-bar-background-sector]:fill-muted [&_.recharts-rectangle.recharts-tooltip-cursor]:fill-muted [&_.recharts-reference-line_[stroke='#ccc']]:stroke-border [&_.recharts-sector[stroke='#fff']]:stroke-transparent [&_.recharts-sector]:outline-none [&_.recharts-surface]:outline-none\",\n          className\n        )}\n        {...props}\n      >\n        <ChartStyle id={chartId} config={config} />\n        <RechartsPrimitive.ResponsiveContainer>\n          {children}\n        </RechartsPrimitive.ResponsiveContainer>\n      </div>\n    </ChartContext.Provider>\n  )\n})\nChartContainer.displayName = \"Chart\"\n\nconst ChartStyle = ({ id, config }: { id: string; config: ChartConfig }) => {\n  const colorConfig = Object.entries(config).filter(\n    ([, config]) => config.theme || config.color\n  )\n\n  if (!colorConfig.length) {\n    return null\n  }\n\n  return (\n    <style\n      dangerouslySetInnerHTML={{\n        __html: Object.entries(THEMES)\n          .map(\n            ([theme, prefix]) => `\n${prefix} [data-chart=${id}] {\n${colorConfig\n  .map(([key, itemConfig]) => {\n    const color =\n      itemConfig.theme?.[theme as keyof typeof itemConfig.theme] ||\n      itemConfig.color\n    return color ? `  --color-${key}: ${color};` : null\n  })\n  .join(\"\\n\")}\n}\n`\n          )\n          .join(\"\\n\"),\n      }}\n    />\n  )\n}\n\nconst ChartTooltip = RechartsPrimitive.Tooltip\n\nconst ChartTooltipContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<typeof RechartsPrimitive.Tooltip> &\n    React.ComponentProps<\"div\"> & {\n      hideLabel?: boolean\n      hideIndicator?: boolean\n      indicator?: \"line\" | \"dot\" | \"dashed\"\n      nameKey?: string\n      labelKey?: string\n    }\n>(\n  (\n    {\n      active,\n      payload,\n      className,\n      indicator = \"dot\",\n      hideLabel = false,\n      hideIndicator = false,\n      label,\n      labelFormatter,\n      labelClassName,\n      formatter,\n      color,\n      nameKey,\n      labelKey,\n    },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    const tooltipLabel = React.useMemo(() => {\n      if (hideLabel || !payload?.length) {\n        return null\n      }\n\n      const [item] = payload\n      const key = `${labelKey || item?.dataKey || item?.name || \"value\"}`\n      const itemConfig = getPayloadConfigFromPayload(config, item, key)\n      const value =\n        !labelKey && typeof label === \"string\"\n          ? config[label as keyof typeof config]?.label || label\n          : itemConfig?.label\n\n      if (labelFormatter) {\n        return (\n          <div className={cn(\"font-medium\", labelClassName)}>\n            {labelFormatter(value, payload)}\n          </div>\n        )\n      }\n\n      if (!value) {\n        return null\n      }\n\n      return <div className={cn(\"font-medium\", labelClassName)}>{value}</div>\n    }, [\n      label,\n      labelFormatter,\n      payload,\n      hideLabel,\n      labelClassName,\n      config,\n      labelKey,\n    ])\n\n    if (!active || !payload?.length) {\n      return null\n    }\n\n    const nestLabel = payload.length === 1 && indicator !== \"dot\"\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"grid min-w-[8rem] items-start gap-1.5 rounded-lg border border-border/50 bg-background px-2.5 py-1.5 text-xs shadow-xl\",\n          className\n        )}\n      >\n        {!nestLabel ? tooltipLabel : null}\n        <div className=\"grid gap-1.5\">\n          {payload.map((item, index) => {\n            const key = `${nameKey || item.name || item.dataKey || \"value\"}`\n            const itemConfig = getPayloadConfigFromPayload(config, item, key)\n            const indicatorColor = color || item.payload.fill || item.color\n\n            return (\n              <div\n                key={item.dataKey}\n                className={cn(\n                  \"flex w-full flex-wrap items-stretch gap-2 [&>svg]:h-2.5 [&>svg]:w-2.5 [&>svg]:text-muted-foreground\",\n                  indicator === \"dot\" && \"items-center\"\n                )}\n              >\n                {formatter && item?.value !== undefined && item.name ? (\n                  formatter(item.value, item.name, item, index, item.payload)\n                ) : (\n                  <>\n                    {itemConfig?.icon ? (\n                      <itemConfig.icon />\n                    ) : (\n                      !hideIndicator && (\n                        <div\n                          className={cn(\n                            \"shrink-0 rounded-[2px] border-[--color-border] bg-[--color-bg]\",\n                            {\n                              \"h-2.5 w-2.5\": indicator === \"dot\",\n                              \"w-1\": indicator === \"line\",\n                              \"w-0 border-[1.5px] border-dashed bg-transparent\":\n                                indicator === \"dashed\",\n                              \"my-0.5\": nestLabel && indicator === \"dashed\",\n                            }\n                          )}\n                          style={\n                            {\n                              \"--color-bg\": indicatorColor,\n                              \"--color-border\": indicatorColor,\n                            } as React.CSSProperties\n                          }\n                        />\n                      )\n                    )}\n                    <div\n                      className={cn(\n                        \"flex flex-1 justify-between leading-none\",\n                        nestLabel ? \"items-end\" : \"items-center\"\n                      )}\n                    >\n                      <div className=\"grid gap-1.5\">\n                        {nestLabel ? tooltipLabel : null}\n                        <span className=\"text-muted-foreground\">\n                          {itemConfig?.label || item.name}\n                        </span>\n                      </div>\n                      {item.value && (\n                        <span className=\"font-mono font-medium tabular-nums text-foreground\">\n                          {item.value.toLocaleString()}\n                        </span>\n                      )}\n                    </div>\n                  </>\n                )}\n              </div>\n            )\n          })}\n        </div>\n      </div>\n    )\n  }\n)\nChartTooltipContent.displayName = \"ChartTooltip\"\n\nconst ChartLegend = RechartsPrimitive.Legend\n\nconst ChartLegendContent = React.forwardRef<\n  HTMLDivElement,\n  React.ComponentProps<\"div\"> &\n    Pick<RechartsPrimitive.LegendProps, \"payload\" | \"verticalAlign\"> & {\n      hideIcon?: boolean\n      nameKey?: string\n    }\n>(\n  (\n    { className, hideIcon = false, payload, verticalAlign = \"bottom\", nameKey },\n    ref\n  ) => {\n    const { config } = useChart()\n\n    if (!payload?.length) {\n      return null\n    }\n\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          \"flex items-center justify-center gap-4\",\n          verticalAlign === \"top\" ? \"pb-3\" : \"pt-3\",\n          className\n        )}\n      >\n        {payload.map((item) => {\n          const key = `${nameKey || item.dataKey || \"value\"}`\n          const itemConfig = getPayloadConfigFromPayload(config, item, key)\n\n          return (\n            <div\n              key={item.value}\n              className={cn(\n                \"flex items-center gap-1.5 [&>svg]:h-3 [&>svg]:w-3 [&>svg]:text-muted-foreground\"\n              )}\n            >\n              {itemConfig?.icon && !hideIcon ? (\n                <itemConfig.icon />\n              ) : (\n                <div\n                  className=\"h-2 w-2 shrink-0 rounded-[2px]\"\n                  style={{\n                    backgroundColor: item.color,\n                  }}\n                />\n              )}\n              {itemConfig?.label}\n            </div>\n          )\n        })}\n      </div>\n    )\n  }\n)\nChartLegendContent.displayName = \"ChartLegend\"\n\n// Helper to extract item config from a payload.\nfunction getPayloadConfigFromPayload(\n  config: ChartConfig,\n  payload: unknown,\n  key: string\n) {\n  if (typeof payload !== \"object\" || payload === null) {\n    return undefined\n  }\n\n  const payloadPayload =\n    \"payload\" in payload &&\n    typeof payload.payload === \"object\" &&\n    payload.payload !== null\n      ? payload.payload\n      : undefined\n\n  let configLabelKey: string = key\n\n  if (\n    key in payload &&\n    typeof payload[key as keyof typeof payload] === \"string\"\n  ) {\n    configLabelKey = payload[key as keyof typeof payload] as string\n  } else if (\n    payloadPayload &&\n    key in payloadPayload &&\n    typeof payloadPayload[key as keyof typeof payloadPayload] === \"string\"\n  ) {\n    configLabelKey = payloadPayload[\n      key as keyof typeof payloadPayload\n    ] as string\n  }\n\n  return configLabelKey in config\n    ? config[configLabelKey]\n    : config[key as keyof typeof config]\n}\n\nexport {\n  ChartContainer,\n  ChartTooltip,\n  ChartTooltipContent,\n  ChartLegend,\n  ChartLegendContent,\n  ChartStyle,\n}\n", "size_bytes": 10481}, "client/src/components/ui/checkbox.tsx": {"content": "import * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n", "size_bytes": 1056}, "client/src/components/ui/collapsible.tsx": {"content": "\"use client\"\n\nimport * as CollapsiblePrimitive from \"@radix-ui/react-collapsible\"\n\nconst Collapsible = CollapsiblePrimitive.Root\n\nconst CollapsibleTrigger = CollapsiblePrimitive.CollapsibleTrigger\n\nconst CollapsibleContent = CollapsiblePrimitive.CollapsibleContent\n\nexport { Collapsible, CollapsibleTrigger, CollapsibleContent }\n", "size_bytes": 329}, "client/src/components/ui/command.tsx": {"content": "import * as React from \"react\"\nimport { type DialogProps } from \"@radix-ui/react-dialog\"\nimport { Command as CommandPrimitive } from \"cmdk\"\nimport { Search } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Dialog, DialogContent } from \"@/components/ui/dialog\"\n\nconst Command = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nCommand.displayName = CommandPrimitive.displayName\n\nconst CommandDialog = ({ children, ...props }: DialogProps) => {\n  return (\n    <Dialog {...props}>\n      <DialogContent className=\"overflow-hidden p-0 shadow-lg\">\n        <Command className=\"[&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground [&_[cmdk-group]:not([hidden])_~[cmdk-group]]:pt-0 [&_[cmdk-group]]:px-2 [&_[cmdk-input-wrapper]_svg]:h-5 [&_[cmdk-input-wrapper]_svg]:w-5 [&_[cmdk-input]]:h-12 [&_[cmdk-item]]:px-2 [&_[cmdk-item]]:py-3 [&_[cmdk-item]_svg]:h-5 [&_[cmdk-item]_svg]:w-5\">\n          {children}\n        </Command>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\nconst CommandInput = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Input>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Input>\n>(({ className, ...props }, ref) => (\n  <div className=\"flex items-center border-b px-3\" cmdk-input-wrapper=\"\">\n    <Search className=\"mr-2 h-4 w-4 shrink-0 opacity-50\" />\n    <CommandPrimitive.Input\n      ref={ref}\n      className={cn(\n        \"flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  </div>\n))\n\nCommandInput.displayName = CommandPrimitive.Input.displayName\n\nconst CommandList = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.List\n    ref={ref}\n    className={cn(\"max-h-[300px] overflow-y-auto overflow-x-hidden\", className)}\n    {...props}\n  />\n))\n\nCommandList.displayName = CommandPrimitive.List.displayName\n\nconst CommandEmpty = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Empty>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Empty>\n>((props, ref) => (\n  <CommandPrimitive.Empty\n    ref={ref}\n    className=\"py-6 text-center text-sm\"\n    {...props}\n  />\n))\n\nCommandEmpty.displayName = CommandPrimitive.Empty.displayName\n\nconst CommandGroup = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Group>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Group>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Group\n    ref={ref}\n    className={cn(\n      \"overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandGroup.displayName = CommandPrimitive.Group.displayName\n\nconst CommandSeparator = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 h-px bg-border\", className)}\n    {...props}\n  />\n))\nCommandSeparator.displayName = CommandPrimitive.Separator.displayName\n\nconst CommandItem = React.forwardRef<\n  React.ElementRef<typeof CommandPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof CommandPrimitive.Item>\n>(({ className, ...props }, ref) => (\n  <CommandPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      className\n    )}\n    {...props}\n  />\n))\n\nCommandItem.displayName = CommandPrimitive.Item.displayName\n\nconst CommandShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nCommandShortcut.displayName = \"CommandShortcut\"\n\nexport {\n  Command,\n  CommandDialog,\n  CommandInput,\n  CommandList,\n  CommandEmpty,\n  CommandGroup,\n  CommandItem,\n  CommandShortcut,\n  CommandSeparator,\n}\n", "size_bytes": 4885}, "client/src/components/ui/context-menu.tsx": {"content": "import * as React from \"react\"\nimport * as ContextMenuPrimitive from \"@radix-ui/react-context-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ContextMenu = ContextMenuPrimitive.Root\n\nconst ContextMenuTrigger = ContextMenuPrimitive.Trigger\n\nconst ContextMenuGroup = ContextMenuPrimitive.Group\n\nconst ContextMenuPortal = ContextMenuPrimitive.Portal\n\nconst ContextMenuSub = ContextMenuPrimitive.Sub\n\nconst ContextMenuRadioGroup = ContextMenuPrimitive.RadioGroup\n\nconst ContextMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <ContextMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </ContextMenuPrimitive.SubTrigger>\n))\nContextMenuSubTrigger.displayName = ContextMenuPrimitive.SubTrigger.displayName\n\nconst ContextMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-context-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuSubContent.displayName = ContextMenuPrimitive.SubContent.displayName\n\nconst ContextMenuContent = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Portal>\n    <ContextMenuPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"z-50 max-h-[--radix-context-menu-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md animate-in fade-in-80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-context-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </ContextMenuPrimitive.Portal>\n))\nContextMenuContent.displayName = ContextMenuPrimitive.Content.displayName\n\nconst ContextMenuItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuItem.displayName = ContextMenuPrimitive.Item.displayName\n\nconst ContextMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <ContextMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <ContextMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.CheckboxItem>\n))\nContextMenuCheckboxItem.displayName =\n  ContextMenuPrimitive.CheckboxItem.displayName\n\nconst ContextMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <ContextMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <ContextMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </ContextMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </ContextMenuPrimitive.RadioItem>\n))\nContextMenuRadioItem.displayName = ContextMenuPrimitive.RadioItem.displayName\n\nconst ContextMenuLabel = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <ContextMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold text-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nContextMenuLabel.displayName = ContextMenuPrimitive.Label.displayName\n\nconst ContextMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof ContextMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof ContextMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <ContextMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-border\", className)}\n    {...props}\n  />\n))\nContextMenuSeparator.displayName = ContextMenuPrimitive.Separator.displayName\n\nconst ContextMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nContextMenuShortcut.displayName = \"ContextMenuShortcut\"\n\nexport {\n  ContextMenu,\n  ContextMenuTrigger,\n  ContextMenuContent,\n  ContextMenuItem,\n  ContextMenuCheckboxItem,\n  ContextMenuRadioItem,\n  ContextMenuLabel,\n  ContextMenuSeparator,\n  ContextMenuShortcut,\n  ContextMenuGroup,\n  ContextMenuPortal,\n  ContextMenuSub,\n  ContextMenuSubContent,\n  ContextMenuSubTrigger,\n  ContextMenuRadioGroup,\n}\n", "size_bytes": 7428}, "client/src/components/ui/dialog.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Dialog = DialogPrimitive.Root\n\nconst DialogTrigger = DialogPrimitive.Trigger\n\nconst DialogPortal = DialogPrimitive.Portal\n\nconst DialogClose = DialogPrimitive.Close\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n))\nDialogContent.displayName = DialogPrimitive.Content.displayName\n\nconst DialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-1.5 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogHeader.displayName = \"DialogHeader\"\n\nconst DialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nDialogFooter.displayName = \"DialogFooter\"\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDialogTitle.displayName = DialogPrimitive.Title.displayName\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDialogDescription.displayName = DialogPrimitive.Description.displayName\n\nexport {\n  Dialog,\n  DialogPortal,\n  DialogOverlay,\n  DialogClose,\n  DialogTrigger,\n  DialogContent,\n  DialogHeader,\n  DialogFooter,\n  DialogTitle,\n  DialogDescription,\n}\n", "size_bytes": 3848}, "client/src/components/ui/drawer.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Drawer as DrawerPrimitive } from \"vaul\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Drawer = ({\n  shouldScaleBackground = true,\n  ...props\n}: React.ComponentProps<typeof DrawerPrimitive.Root>) => (\n  <DrawerPrimitive.Root\n    shouldScaleBackground={shouldScaleBackground}\n    {...props}\n  />\n)\nDrawer.displayName = \"Drawer\"\n\nconst DrawerTrigger = DrawerPrimitive.Trigger\n\nconst DrawerPortal = DrawerPrimitive.Portal\n\nconst DrawerClose = DrawerPrimitive.Close\n\nconst DrawerOverlay = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Overlay\n    ref={ref}\n    className={cn(\"fixed inset-0 z-50 bg-black/80\", className)}\n    {...props}\n  />\n))\nDrawerOverlay.displayName = DrawerPrimitive.Overlay.displayName\n\nconst DrawerContent = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DrawerPortal>\n    <DrawerOverlay />\n    <DrawerPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed inset-x-0 bottom-0 z-50 mt-24 flex h-auto flex-col rounded-t-[10px] border bg-background\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"mx-auto mt-4 h-2 w-[100px] rounded-full bg-muted\" />\n      {children}\n    </DrawerPrimitive.Content>\n  </DrawerPortal>\n))\nDrawerContent.displayName = \"DrawerContent\"\n\nconst DrawerHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"grid gap-1.5 p-4 text-center sm:text-left\", className)}\n    {...props}\n  />\n)\nDrawerHeader.displayName = \"DrawerHeader\"\n\nconst DrawerFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n    {...props}\n  />\n)\nDrawerFooter.displayName = \"DrawerFooter\"\n\nconst DrawerTitle = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Title\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n))\nDrawerTitle.displayName = DrawerPrimitive.Title.displayName\n\nconst DrawerDescription = React.forwardRef<\n  React.ElementRef<typeof DrawerPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DrawerPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DrawerPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nDrawerDescription.displayName = DrawerPrimitive.Description.displayName\n\nexport {\n  Drawer,\n  DrawerPortal,\n  DrawerOverlay,\n  DrawerTrigger,\n  DrawerClose,\n  DrawerContent,\n  DrawerHeader,\n  DrawerFooter,\n  DrawerTitle,\n  DrawerDescription,\n}\n", "size_bytes": 3021}, "client/src/components/ui/dropdown-menu.tsx": {"content": "import * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst DropdownMenu = DropdownMenuPrimitive.Root\n\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\n\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\n\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\n\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\n\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\n\nconst DropdownMenuSubTrigger = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto\" />\n  </DropdownMenuPrimitive.SubTrigger>\n))\nDropdownMenuSubTrigger.displayName =\n  DropdownMenuPrimitive.SubTrigger.displayName\n\nconst DropdownMenuSubContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuSubContent.displayName =\n  DropdownMenuPrimitive.SubContent.displayName\n\nconst DropdownMenuContent = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <DropdownMenuPrimitive.Portal>\n    <DropdownMenuPrimitive.Content\n      ref={ref}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 max-h-[var(--radix-dropdown-menu-content-available-height)] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-dropdown-menu-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </DropdownMenuPrimitive.Portal>\n))\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\n\nconst DropdownMenuItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\n\nconst DropdownMenuCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <DropdownMenuPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.CheckboxItem>\n))\nDropdownMenuCheckboxItem.displayName =\n  DropdownMenuPrimitive.CheckboxItem.displayName\n\nconst DropdownMenuRadioItem = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <DropdownMenuPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <DropdownMenuPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </DropdownMenuPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </DropdownMenuPrimitive.RadioItem>\n))\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\n\nconst DropdownMenuLabel = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <DropdownMenuPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\n\nconst DropdownMenuSeparator = React.forwardRef<\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <DropdownMenuPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\n\nconst DropdownMenuShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\n      {...props}\n    />\n  )\n}\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuGroup,\n  DropdownMenuPortal,\n  DropdownMenuSub,\n  DropdownMenuSubContent,\n  DropdownMenuSubTrigger,\n  DropdownMenuRadioGroup,\n}\n", "size_bytes": 7609}, "client/src/components/ui/form.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport {\n  Controller,\n  FormProvider,\n  useFormContext,\n  type ControllerProps,\n  type FieldPath,\n  type FieldValues,\n} from \"react-hook-form\"\n\nimport { cn } from \"@/lib/utils\"\nimport { Label } from \"@/components/ui/label\"\n\nconst Form = FormProvider\n\ntype FormFieldContextValue<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n> = {\n  name: TName\n}\n\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\n  {} as FormFieldContextValue\n)\n\nconst FormField = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\n>({\n  ...props\n}: ControllerProps<TFieldValues, TName>) => {\n  return (\n    <FormFieldContext.Provider value={{ name: props.name }}>\n      <Controller {...props} />\n    </FormFieldContext.Provider>\n  )\n}\n\nconst useFormField = () => {\n  const fieldContext = React.useContext(FormFieldContext)\n  const itemContext = React.useContext(FormItemContext)\n  const { getFieldState, formState } = useFormContext()\n\n  const fieldState = getFieldState(fieldContext.name, formState)\n\n  if (!fieldContext) {\n    throw new Error(\"useFormField should be used within <FormField>\")\n  }\n\n  const { id } = itemContext\n\n  return {\n    id,\n    name: fieldContext.name,\n    formItemId: `${id}-form-item`,\n    formDescriptionId: `${id}-form-item-description`,\n    formMessageId: `${id}-form-item-message`,\n    ...fieldState,\n  }\n}\n\ntype FormItemContextValue = {\n  id: string\n}\n\nconst FormItemContext = React.createContext<FormItemContextValue>(\n  {} as FormItemContextValue\n)\n\nconst FormItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => {\n  const id = React.useId()\n\n  return (\n    <FormItemContext.Provider value={{ id }}>\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\n    </FormItemContext.Provider>\n  )\n})\nFormItem.displayName = \"FormItem\"\n\nconst FormLabel = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  const { error, formItemId } = useFormField()\n\n  return (\n    <Label\n      ref={ref}\n      className={cn(error && \"text-destructive\", className)}\n      htmlFor={formItemId}\n      {...props}\n    />\n  )\n})\nFormLabel.displayName = \"FormLabel\"\n\nconst FormControl = React.forwardRef<\n  React.ElementRef<typeof Slot>,\n  React.ComponentPropsWithoutRef<typeof Slot>\n>(({ ...props }, ref) => {\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\n\n  return (\n    <Slot\n      ref={ref}\n      id={formItemId}\n      aria-describedby={\n        !error\n          ? `${formDescriptionId}`\n          : `${formDescriptionId} ${formMessageId}`\n      }\n      aria-invalid={!!error}\n      {...props}\n    />\n  )\n})\nFormControl.displayName = \"FormControl\"\n\nconst FormDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => {\n  const { formDescriptionId } = useFormField()\n\n  return (\n    <p\n      ref={ref}\n      id={formDescriptionId}\n      className={cn(\"text-sm text-muted-foreground\", className)}\n      {...props}\n    />\n  )\n})\nFormDescription.displayName = \"FormDescription\"\n\nconst FormMessage = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, children, ...props }, ref) => {\n  const { error, formMessageId } = useFormField()\n  const body = error ? String(error?.message ?? \"\") : children\n\n  if (!body) {\n    return null\n  }\n\n  return (\n    <p\n      ref={ref}\n      id={formMessageId}\n      className={cn(\"text-sm font-medium text-destructive\", className)}\n      {...props}\n    >\n      {body}\n    </p>\n  )\n})\nFormMessage.displayName = \"FormMessage\"\n\nexport {\n  useFormField,\n  Form,\n  FormItem,\n  FormLabel,\n  FormControl,\n  FormDescription,\n  FormMessage,\n  FormField,\n}\n", "size_bytes": 4120}, "client/src/components/ui/hover-card.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as HoverCardPrimitive from \"@radix-ui/react-hover-card\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst HoverCard = HoverCardPrimitive.Root\n\nconst HoverCardTrigger = HoverCardPrimitive.Trigger\n\nconst HoverCardContent = React.forwardRef<\n  React.ElementRef<typeof HoverCardPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof HoverCardPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <HoverCardPrimitive.Content\n    ref={ref}\n    align={align}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 w-64 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-hover-card-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nHoverCardContent.displayName = HoverCardPrimitive.Content.displayName\n\nexport { HoverCard, HoverCardTrigger, HoverCardContent }\n", "size_bytes": 1251}, "client/src/components/ui/input-otp.tsx": {"content": "import * as React from \"react\"\nimport { OTPInput, OTPInputContext } from \"input-otp\"\nimport { Dot } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst InputOTP = React.forwardRef<\n  React.ElementRef<typeof OTPInput>,\n  React.ComponentPropsWithoutRef<typeof OTPInput>\n>(({ className, containerClassName, ...props }, ref) => (\n  <OTPInput\n    ref={ref}\n    containerClassName={cn(\n      \"flex items-center gap-2 has-[:disabled]:opacity-50\",\n      containerClassName\n    )}\n    className={cn(\"disabled:cursor-not-allowed\", className)}\n    {...props}\n  />\n))\nInputOTP.displayName = \"InputOTP\"\n\nconst InputOTPGroup = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center\", className)} {...props} />\n))\nInputOTPGroup.displayName = \"InputOTPGroup\"\n\nconst InputOTPSlot = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\"> & { index: number }\n>(({ index, className, ...props }, ref) => {\n  const inputOTPContext = React.useContext(OTPInputContext)\n  const { char, hasFakeCaret, isActive } = inputOTPContext.slots[index]\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex h-10 w-10 items-center justify-center border-y border-r border-input text-sm transition-all first:rounded-l-md first:border-l last:rounded-r-md\",\n        isActive && \"z-10 ring-2 ring-ring ring-offset-background\",\n        className\n      )}\n      {...props}\n    >\n      {char}\n      {hasFakeCaret && (\n        <div className=\"pointer-events-none absolute inset-0 flex items-center justify-center\">\n          <div className=\"h-4 w-px animate-caret-blink bg-foreground duration-1000\" />\n        </div>\n      )}\n    </div>\n  )\n})\nInputOTPSlot.displayName = \"InputOTPSlot\"\n\nconst InputOTPSeparator = React.forwardRef<\n  React.ElementRef<\"div\">,\n  React.ComponentPropsWithoutRef<\"div\">\n>(({ ...props }, ref) => (\n  <div ref={ref} role=\"separator\" {...props}>\n    <Dot />\n  </div>\n))\nInputOTPSeparator.displayName = \"InputOTPSeparator\"\n\nexport { InputOTP, InputOTPGroup, InputOTPSlot, InputOTPSeparator }\n", "size_bytes": 2154}, "client/src/components/ui/input.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\n  ({ className, type, ...props }, ref) => {\n    // h-9 to match icon buttons and default buttons.\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-9 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n", "size_bytes": 844}, "client/src/components/ui/label.tsx": {"content": "import * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n", "size_bytes": 710}, "client/src/components/ui/menubar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as MenubarPrimitive from \"@radix-ui/react-menubar\"\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction MenubarMenu({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Menu>) {\n  return <MenubarPrimitive.Menu {...props} />\n}\n\nfunction MenubarGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Group>) {\n  return <MenubarPrimitive.Group {...props} />\n}\n\nfunction MenubarPortal({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Portal>) {\n  return <MenubarPrimitive.Portal {...props} />\n}\n\nfunction MenubarRadioGroup({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.RadioGroup>) {\n  return <MenubarPrimitive.RadioGroup {...props} />\n}\n\nfunction MenubarSub({\n  ...props\n}: React.ComponentProps<typeof MenubarPrimitive.Sub>) {\n  return <MenubarPrimitive.Sub data-slot=\"menubar-sub\" {...props} />\n}\n\nconst Menubar = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"flex h-10 items-center space-x-1 rounded-md border bg-background p-1\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubar.displayName = MenubarPrimitive.Root.displayName\n\nconst MenubarTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-3 py-1.5 text-sm font-medium outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarTrigger.displayName = MenubarPrimitive.Trigger.displayName\n\nconst MenubarSubTrigger = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubTrigger>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubTrigger> & {\n    inset?: boolean\n  }\n>(({ className, inset, children, ...props }, ref) => (\n  <MenubarPrimitive.SubTrigger\n    ref={ref}\n    className={cn(\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\n  </MenubarPrimitive.SubTrigger>\n))\nMenubarSubTrigger.displayName = MenubarPrimitive.SubTrigger.displayName\n\nconst MenubarSubContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.SubContent>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.SubContent>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.SubContent\n    ref={ref}\n    className={cn(\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-menubar-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarSubContent.displayName = MenubarPrimitive.SubContent.displayName\n\nconst MenubarContent = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Content>\n>(\n  (\n    { className, align = \"start\", alignOffset = -4, sideOffset = 8, ...props },\n    ref\n  ) => (\n    <MenubarPrimitive.Portal>\n      <MenubarPrimitive.Content\n        ref={ref}\n        align={align}\n        alignOffset={alignOffset}\n        sideOffset={sideOffset}\n        className={cn(\n          \"z-50 min-w-[12rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-menubar-content-transform-origin]\",\n          className\n        )}\n        {...props}\n      />\n    </MenubarPrimitive.Portal>\n  )\n)\nMenubarContent.displayName = MenubarPrimitive.Content.displayName\n\nconst MenubarItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Item> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarItem.displayName = MenubarPrimitive.Item.displayName\n\nconst MenubarCheckboxItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.CheckboxItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.CheckboxItem>\n>(({ className, children, checked, ...props }, ref) => (\n  <MenubarPrimitive.CheckboxItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    checked={checked}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <MenubarPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.CheckboxItem>\n))\nMenubarCheckboxItem.displayName = MenubarPrimitive.CheckboxItem.displayName\n\nconst MenubarRadioItem = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.RadioItem>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.RadioItem>\n>(({ className, children, ...props }, ref) => (\n  <MenubarPrimitive.RadioItem\n    ref={ref}\n    className={cn(\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <MenubarPrimitive.ItemIndicator>\n        <Circle className=\"h-2 w-2 fill-current\" />\n      </MenubarPrimitive.ItemIndicator>\n    </span>\n    {children}\n  </MenubarPrimitive.RadioItem>\n))\nMenubarRadioItem.displayName = MenubarPrimitive.RadioItem.displayName\n\nconst MenubarLabel = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Label> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <MenubarPrimitive.Label\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nMenubarLabel.displayName = MenubarPrimitive.Label.displayName\n\nconst MenubarSeparator = React.forwardRef<\n  React.ElementRef<typeof MenubarPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof MenubarPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <MenubarPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nMenubarSeparator.displayName = MenubarPrimitive.Separator.displayName\n\nconst MenubarShortcut = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLSpanElement>) => {\n  return (\n    <span\n      className={cn(\n        \"ml-auto text-xs tracking-widest text-muted-foreground\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\nMenubarShortcut.displayname = \"MenubarShortcut\"\n\nexport {\n  Menubar,\n  MenubarMenu,\n  MenubarTrigger,\n  MenubarContent,\n  MenubarItem,\n  MenubarSeparator,\n  MenubarLabel,\n  MenubarCheckboxItem,\n  MenubarRadioGroup,\n  MenubarRadioItem,\n  MenubarPortal,\n  MenubarSubContent,\n  MenubarSubTrigger,\n  MenubarGroup,\n  MenubarSub,\n  MenubarShortcut,\n}\n", "size_bytes": 8605}, "client/src/components/ui/navigation-menu.tsx": {"content": "import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDown } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst NavigationMenu = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative z-10 flex max-w-max flex-1 items-center justify-center\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <NavigationMenuViewport />\n  </NavigationMenuPrimitive.Root>\n))\nNavigationMenu.displayName = NavigationMenuPrimitive.Root.displayName\n\nconst NavigationMenuList = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.List\n    ref={ref}\n    className={cn(\n      \"group flex flex-1 list-none items-center justify-center space-x-1\",\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuList.displayName = NavigationMenuPrimitive.List.displayName\n\nconst NavigationMenuItem = NavigationMenuPrimitive.Item\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[state=open]:text-accent-foreground data-[state=open]:bg-accent/50 data-[state=open]:hover:bg-accent data-[state=open]:focus:bg-accent\"\n)\n\nconst NavigationMenuTrigger = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <NavigationMenuPrimitive.Trigger\n    ref={ref}\n    className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n    {...props}\n  >\n    {children}{\" \"}\n    <ChevronDown\n      className=\"relative top-[1px] ml-1 h-3 w-3 transition duration-200 group-data-[state=open]:rotate-180\"\n      aria-hidden=\"true\"\n    />\n  </NavigationMenuPrimitive.Trigger>\n))\nNavigationMenuTrigger.displayName = NavigationMenuPrimitive.Trigger.displayName\n\nconst NavigationMenuContent = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"left-0 top-0 w-full data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 md:absolute md:w-auto \",\n      className\n    )}\n    {...props}\n  />\n))\nNavigationMenuContent.displayName = NavigationMenuPrimitive.Content.displayName\n\nconst NavigationMenuLink = NavigationMenuPrimitive.Link\n\nconst NavigationMenuViewport = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Viewport>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Viewport>\n>(({ className, ...props }, ref) => (\n  <div className={cn(\"absolute left-0 top-full flex justify-center\")}>\n    <NavigationMenuPrimitive.Viewport\n      className={cn(\n        \"origin-top-center relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 md:w-[var(--radix-navigation-menu-viewport-width)]\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  </div>\n))\nNavigationMenuViewport.displayName =\n  NavigationMenuPrimitive.Viewport.displayName\n\nconst NavigationMenuIndicator = React.forwardRef<\n  React.ElementRef<typeof NavigationMenuPrimitive.Indicator>,\n  React.ComponentPropsWithoutRef<typeof NavigationMenuPrimitive.Indicator>\n>(({ className, ...props }, ref) => (\n  <NavigationMenuPrimitive.Indicator\n    ref={ref}\n    className={cn(\n      \"top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in\",\n      className\n    )}\n    {...props}\n  >\n    <div className=\"relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm bg-border shadow-md\" />\n  </NavigationMenuPrimitive.Indicator>\n))\nNavigationMenuIndicator.displayName =\n  NavigationMenuPrimitive.Indicator.displayName\n\nexport {\n  navigationMenuTriggerStyle,\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n}\n", "size_bytes": 5128}, "client/src/components/ui/pagination.tsx": {"content": "import * as React from \"react\"\nimport { ChevronLeft, ChevronRight, MoreHorizontal } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\nimport { ButtonProps, buttonVariants } from \"@/components/ui/button\"\n\nconst Pagination = ({ className, ...props }: React.ComponentProps<\"nav\">) => (\n  <nav\n    role=\"navigation\"\n    aria-label=\"pagination\"\n    className={cn(\"mx-auto flex w-full justify-center\", className)}\n    {...props}\n  />\n)\nPagination.displayName = \"Pagination\"\n\nconst PaginationContent = React.forwardRef<\n  HTMLUListElement,\n  React.ComponentProps<\"ul\">\n>(({ className, ...props }, ref) => (\n  <ul\n    ref={ref}\n    className={cn(\"flex flex-row items-center gap-1\", className)}\n    {...props}\n  />\n))\nPaginationContent.displayName = \"PaginationContent\"\n\nconst PaginationItem = React.forwardRef<\n  HTMLLIElement,\n  React.ComponentProps<\"li\">\n>(({ className, ...props }, ref) => (\n  <li ref={ref} className={cn(\"\", className)} {...props} />\n))\nPaginationItem.displayName = \"PaginationItem\"\n\ntype PaginationLinkProps = {\n  isActive?: boolean\n} & Pick<ButtonProps, \"size\"> &\n  React.ComponentProps<\"a\">\n\nconst PaginationLink = ({\n  className,\n  isActive,\n  size = \"icon\",\n  ...props\n}: PaginationLinkProps) => (\n  <a\n    aria-current={isActive ? \"page\" : undefined}\n    className={cn(\n      buttonVariants({\n        variant: isActive ? \"outline\" : \"ghost\",\n        size,\n      }),\n      className\n    )}\n    {...props}\n  />\n)\nPaginationLink.displayName = \"PaginationLink\"\n\nconst PaginationPrevious = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to previous page\"\n    size=\"default\"\n    className={cn(\"gap-1 pl-2.5\", className)}\n    {...props}\n  >\n    <ChevronLeft className=\"h-4 w-4\" />\n    <span>Previous</span>\n  </PaginationLink>\n)\nPaginationPrevious.displayName = \"PaginationPrevious\"\n\nconst PaginationNext = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof PaginationLink>) => (\n  <PaginationLink\n    aria-label=\"Go to next page\"\n    size=\"default\"\n    className={cn(\"gap-1 pr-2.5\", className)}\n    {...props}\n  >\n    <span>Next</span>\n    <ChevronRight className=\"h-4 w-4\" />\n  </PaginationLink>\n)\nPaginationNext.displayName = \"PaginationNext\"\n\nconst PaginationEllipsis = ({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) => (\n  <span\n    aria-hidden\n    className={cn(\"flex h-9 w-9 items-center justify-center\", className)}\n    {...props}\n  >\n    <MoreHorizontal className=\"h-4 w-4\" />\n    <span className=\"sr-only\">More pages</span>\n  </span>\n)\nPaginationEllipsis.displayName = \"PaginationEllipsis\"\n\nexport {\n  Pagination,\n  PaginationContent,\n  PaginationEllipsis,\n  PaginationItem,\n  PaginationLink,\n  PaginationNext,\n  PaginationPrevious,\n}\n", "size_bytes": 2751}, "client/src/components/ui/popover.tsx": {"content": "import * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent }\n", "size_bytes": 1280}, "client/src/components/ui/progress.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Progress = React.forwardRef<\n  React.ElementRef<typeof ProgressPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\n>(({ className, value, ...props }, ref) => (\n  <ProgressPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\n      className\n    )}\n    {...props}\n  >\n    <ProgressPrimitive.Indicator\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\n    />\n  </ProgressPrimitive.Root>\n))\nProgress.displayName = ProgressPrimitive.Root.displayName\n\nexport { Progress }\n", "size_bytes": 791}, "client/src/components/ui/radio-group.tsx": {"content": "import * as React from \"react\"\nimport * as RadioGroupPrimitive from \"@radix-ui/react-radio-group\"\nimport { Circle } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst RadioGroup = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Root>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Root\n      className={cn(\"grid gap-2\", className)}\n      {...props}\n      ref={ref}\n    />\n  )\n})\nRadioGroup.displayName = RadioGroupPrimitive.Root.displayName\n\nconst RadioGroupItem = React.forwardRef<\n  React.ElementRef<typeof RadioGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof RadioGroupPrimitive.Item>\n>(({ className, ...props }, ref) => {\n  return (\n    <RadioGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        \"aspect-square h-4 w-4 rounded-full border border-primary text-primary ring-offset-background focus:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <RadioGroupPrimitive.Indicator className=\"flex items-center justify-center\">\n        <Circle className=\"h-2.5 w-2.5 fill-current text-current\" />\n      </RadioGroupPrimitive.Indicator>\n    </RadioGroupPrimitive.Item>\n  )\n})\nRadioGroupItem.displayName = RadioGroupPrimitive.Item.displayName\n\nexport { RadioGroup, RadioGroupItem }\n", "size_bytes": 1467}, "client/src/components/ui/resizable.tsx": {"content": "\"use client\"\n\nimport { GripVertical } from \"lucide-react\"\nimport * as ResizablePrimitive from \"react-resizable-panels\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ResizablePanelGroup = ({\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelGroup>) => (\n  <ResizablePrimitive.PanelGroup\n    className={cn(\n      \"flex h-full w-full data-[panel-group-direction=vertical]:flex-col\",\n      className\n    )}\n    {...props}\n  />\n)\n\nconst ResizablePanel = ResizablePrimitive.Panel\n\nconst ResizableHandle = ({\n  withHandle,\n  className,\n  ...props\n}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle> & {\n  withHandle?: boolean\n}) => (\n  <ResizablePrimitive.PanelResizeHandle\n    className={cn(\n      \"relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring focus-visible:ring-offset-1 data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90\",\n      className\n    )}\n    {...props}\n  >\n    {withHandle && (\n      <div className=\"z-10 flex h-4 w-3 items-center justify-center rounded-sm border bg-border\">\n        <GripVertical className=\"h-2.5 w-2.5\" />\n      </div>\n    )}\n  </ResizablePrimitive.PanelResizeHandle>\n)\n\nexport { ResizablePanelGroup, ResizablePanel, ResizableHandle }\n", "size_bytes": 1723}, "client/src/components/ui/scroll-area.tsx": {"content": "import * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n", "size_bytes": 1642}, "client/src/components/ui/select.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-9 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\"\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n", "size_bytes": 5741}, "client/src/components/ui/separator.tsx": {"content": "import * as React from \"react\"\nimport * as SeparatorPrimitive from \"@radix-ui/react-separator\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  React.ElementRef<typeof SeparatorPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SeparatorPrimitive.Root>\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <SeparatorPrimitive.Root\n      ref={ref}\n      decorative={decorative}\n      orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = SeparatorPrimitive.Root.displayName\n\nexport { Separator }\n", "size_bytes": 756}, "client/src/components/ui/sheet.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Sheet = SheetPrimitive.Root\n\nconst SheetTrigger = SheetPrimitive.Trigger\n\nconst SheetClose = SheetPrimitive.Close\n\nconst SheetPortal = SheetPrimitive.Portal\n\nconst SheetOverlay = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nSheetOverlay.displayName = SheetPrimitive.Overlay.displayName\n\nconst sheetVariants = cva(\n  \"fixed z-50 gap-4 bg-background p-6 shadow-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n  {\n    variants: {\n      side: {\n        top: \"inset-x-0 top-0 border-b data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top\",\n        bottom:\n          \"inset-x-0 bottom-0 border-t data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom\",\n        left: \"inset-y-0 left-0 h-full w-3/4 border-r data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm\",\n        right:\n          \"inset-y-0 right-0 h-full w-3/4  border-l data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm\",\n      },\n    },\n    defaultVariants: {\n      side: \"right\",\n    },\n  }\n)\n\ninterface SheetContentProps\n  extends React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content>,\n    VariantProps<typeof sheetVariants> {}\n\nconst SheetContent = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Content>,\n  SheetContentProps\n>(({ side = \"right\", className, children, ...props }, ref) => (\n  <SheetPortal>\n    <SheetOverlay />\n    <SheetPrimitive.Content\n      ref={ref}\n      className={cn(sheetVariants({ side }), className)}\n      {...props}\n    >\n      {children}\n      <SheetPrimitive.Close className=\"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary\">\n        <X className=\"h-4 w-4\" />\n        <span className=\"sr-only\">Close</span>\n      </SheetPrimitive.Close>\n    </SheetPrimitive.Content>\n  </SheetPortal>\n))\nSheetContent.displayName = SheetPrimitive.Content.displayName\n\nconst SheetHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetHeader.displayName = \"SheetHeader\"\n\nconst SheetFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nSheetFooter.displayName = \"SheetFooter\"\n\nconst SheetTitle = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold text-foreground\", className)}\n    {...props}\n  />\n))\nSheetTitle.displayName = SheetPrimitive.Title.displayName\n\nconst SheetDescription = React.forwardRef<\n  React.ElementRef<typeof SheetPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <SheetPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nSheetDescription.displayName = SheetPrimitive.Description.displayName\n\nexport {\n  Sheet,\n  SheetPortal,\n  SheetOverlay,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n", "size_bytes": 4281}, "client/src/components/ui/sidebar.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, VariantProps } from \"class-variance-authority\"\nimport { PanelLeftIcon } from \"lucide-react\"\n\nimport { useIsMobile } from \"@/hooks/use-mobile\"\nimport { cn } from \"@/lib/utils\"\nimport { Button } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Separator } from \"@/components/ui/separator\"\nimport {\n  Sheet,\n  SheetContent,\n  SheetDescription,\n  SheetHeader,\n  SheetTitle,\n} from \"@/components/ui/sheet\"\nimport { Skeleton } from \"@/components/ui/skeleton\"\nimport {\n  Toolt<PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\n\nconst SIDEBAR_COOKIE_NAME = \"sidebar_state\"\nconst SIDEBAR_COOKIE_MAX_AGE = 60 * 60 * 24 * 7\nconst SIDEBAR_WIDTH = \"16rem\"\nconst SIDEBAR_WIDTH_MOBILE = \"18rem\"\nconst SIDEBAR_WIDTH_ICON = \"3rem\"\nconst SIDEBAR_KEYBOARD_SHORTCUT = \"b\"\n\ntype SidebarContextProps = {\n  state: \"expanded\" | \"collapsed\"\n  open: boolean\n  setOpen: (open: boolean) => void\n  openMobile: boolean\n  setOpenMobile: (open: boolean) => void\n  isMobile: boolean\n  toggleSidebar: () => void\n}\n\nconst SidebarContext = React.createContext<SidebarContextProps | null>(null)\n\nfunction useSidebar() {\n  const context = React.useContext(SidebarContext)\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider.\")\n  }\n\n  return context\n}\n\nfunction SidebarProvider({\n  defaultOpen = true,\n  open: openProp,\n  onOpenChange: setOpenProp,\n  className,\n  style,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  defaultOpen?: boolean\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}) {\n  const isMobile = useIsMobile()\n  const [openMobile, setOpenMobile] = React.useState(false)\n\n  // This is the internal state of the sidebar.\n  // We use openProp and setOpenProp for control from outside the component.\n  const [_open, _setOpen] = React.useState(defaultOpen)\n  const open = openProp ?? _open\n  const setOpen = React.useCallback(\n    (value: boolean | ((value: boolean) => boolean)) => {\n      const openState = typeof value === \"function\" ? value(open) : value\n      if (setOpenProp) {\n        setOpenProp(openState)\n      } else {\n        _setOpen(openState)\n      }\n\n      // This sets the cookie to keep the sidebar state.\n      document.cookie = `${SIDEBAR_COOKIE_NAME}=${openState}; path=/; max-age=${SIDEBAR_COOKIE_MAX_AGE}`\n    },\n    [setOpenProp, open]\n  )\n\n  // Helper to toggle the sidebar.\n  const toggleSidebar = React.useCallback(() => {\n    return isMobile ? setOpenMobile((open) => !open) : setOpen((open) => !open)\n  }, [isMobile, setOpen, setOpenMobile])\n\n  // Adds a keyboard shortcut to toggle the sidebar.\n  React.useEffect(() => {\n    const handleKeyDown = (event: KeyboardEvent) => {\n      if (\n        event.key === SIDEBAR_KEYBOARD_SHORTCUT &&\n        (event.metaKey || event.ctrlKey)\n      ) {\n        event.preventDefault()\n        toggleSidebar()\n      }\n    }\n\n    window.addEventListener(\"keydown\", handleKeyDown)\n    return () => window.removeEventListener(\"keydown\", handleKeyDown)\n  }, [toggleSidebar])\n\n  // We add a state so that we can do data-state=\"expanded\" or \"collapsed\".\n  // This makes it easier to style the sidebar with Tailwind classes.\n  const state = open ? \"expanded\" : \"collapsed\"\n\n  const contextValue = React.useMemo<SidebarContextProps>(\n    () => ({\n      state,\n      open,\n      setOpen,\n      isMobile,\n      openMobile,\n      setOpenMobile,\n      toggleSidebar,\n    }),\n    [state, open, setOpen, isMobile, openMobile, setOpenMobile, toggleSidebar]\n  )\n\n  return (\n    <SidebarContext.Provider value={contextValue}>\n      <TooltipProvider delayDuration={0}>\n        <div\n          data-slot=\"sidebar-wrapper\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH,\n              \"--sidebar-width-icon\": SIDEBAR_WIDTH_ICON,\n              ...style,\n            } as React.CSSProperties\n          }\n          className={cn(\n            \"group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full\",\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </div>\n      </TooltipProvider>\n    </SidebarContext.Provider>\n  )\n}\n\nfunction Sidebar({\n  side = \"left\",\n  variant = \"sidebar\",\n  collapsible = \"offcanvas\",\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  side?: \"left\" | \"right\"\n  variant?: \"sidebar\" | \"floating\" | \"inset\"\n  collapsible?: \"offcanvas\" | \"icon\" | \"none\"\n}) {\n  const { isMobile, state, openMobile, setOpenMobile } = useSidebar()\n\n  if (collapsible === \"none\") {\n    return (\n      <div\n        data-slot=\"sidebar\"\n        className={cn(\n          \"bg-sidebar text-sidebar-foreground flex h-full w-[var(--sidebar-width)] flex-col\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    )\n  }\n\n  if (isMobile) {\n    return (\n      <Sheet open={openMobile} onOpenChange={setOpenMobile} {...props}>\n        <SheetContent\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar\"\n          data-mobile=\"true\"\n          className=\"bg-sidebar text-sidebar-foreground w-[var(--sidebar-width)] p-0 [&>button]:hidden\"\n          style={\n            {\n              \"--sidebar-width\": SIDEBAR_WIDTH_MOBILE,\n            } as React.CSSProperties\n          }\n          side={side}\n        >\n          <SheetHeader className=\"sr-only\">\n            <SheetTitle>Sidebar</SheetTitle>\n            <SheetDescription>Displays the mobile sidebar.</SheetDescription>\n          </SheetHeader>\n          <div className=\"flex h-full w-full flex-col\">{children}</div>\n        </SheetContent>\n      </Sheet>\n    )\n  }\n\n  return (\n    <div\n      className=\"group peer text-sidebar-foreground hidden md:block\"\n      data-state={state}\n      data-collapsible={state === \"collapsed\" ? collapsible : \"\"}\n      data-variant={variant}\n      data-side={side}\n      data-slot=\"sidebar\"\n    >\n      {/* This is what handles the sidebar gap on desktop */}\n      <div\n        data-slot=\"sidebar-gap\"\n        className={cn(\n          \"relative w-[var(--sidebar-width)] bg-transparent transition-[width] duration-200 ease-linear\",\n          \"group-data-[collapsible=offcanvas]:w-0\",\n          \"group-data-[side=right]:rotate-180\",\n          variant === \"floating\" || variant === \"inset\"\n            ? \"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+var(--spacing-4))]\"\n            : \"group-data-[collapsible=icon]:w-[var(--sidebar-width-icon)]\"\n        )}\n      />\n      <div\n        data-slot=\"sidebar-container\"\n        className={cn(\n          \"fixed inset-y-0 z-10 hidden h-svh w-[var(--sidebar-width)] transition-[left,right,width] duration-200 ease-linear md:flex\",\n          side === \"left\"\n            ? \"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]\"\n            : \"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]\",\n          // Adjust the padding for floating and inset variants.\n          variant === \"floating\" || variant === \"inset\"\n            ? \"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+var(--spacing-4)+2px)]\"\n            : \"group-data-[collapsible=icon]:w-[var(--sidebar-width-icon)] group-data-[side=left]:border-r group-data-[side=right]:border-l\",\n          className\n        )}\n        {...props}\n      >\n        <div\n          data-sidebar=\"sidebar\"\n          data-slot=\"sidebar-inner\"\n          className=\"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm\"\n        >\n          {children}\n        </div>\n      </div>\n    </div>\n  )\n}\n\nfunction SidebarTrigger({\n  className,\n  onClick,\n  ...props\n}: React.ComponentProps<typeof Button>) {\n  const { toggleSidebar } = useSidebar()\n\n  return (\n    <Button\n      data-sidebar=\"trigger\"\n      data-slot=\"sidebar-trigger\"\n      variant=\"ghost\"\n      size=\"icon\"\n      className={cn(\"h-7 w-7\", className)}\n      onClick={(event) => {\n        onClick?.(event)\n        toggleSidebar()\n      }}\n      {...props}\n    >\n      <PanelLeftIcon />\n      <span className=\"sr-only\">Toggle Sidebar</span>\n    </Button>\n  )\n}\n\nfunction SidebarRail({ className, ...props }: React.ComponentProps<\"button\">) {\n  const { toggleSidebar } = useSidebar()\n\n  // Note: Tailwind v3.4 doesn't support \"in-\" selectors. So the rail won't work perfectly.\n  return (\n    <button\n      data-sidebar=\"rail\"\n      data-slot=\"sidebar-rail\"\n      aria-label=\"Toggle Sidebar\"\n      tabIndex={-1}\n      onClick={toggleSidebar}\n      title=\"Toggle Sidebar\"\n      className={cn(\n        \"hover:after:bg-sidebar-border absolute inset-y-0 z-20 hidden w-4 -translate-x-1/2 transition-all ease-linear group-data-[side=left]:-right-4 group-data-[side=right]:left-0 after:absolute after:inset-y-0 after:left-1/2 after:w-[2px] sm:flex\",\n        \"in-data-[side=left]:cursor-w-resize in-data-[side=right]:cursor-e-resize\",\n        \"[[data-side=left][data-state=collapsed]_&]:cursor-e-resize [[data-side=right][data-state=collapsed]_&]:cursor-w-resize\",\n        \"hover:group-data-[collapsible=offcanvas]:bg-sidebar group-data-[collapsible=offcanvas]:translate-x-0 group-data-[collapsible=offcanvas]:after:left-full\",\n        \"[[data-side=left][data-collapsible=offcanvas]_&]:-right-2\",\n        \"[[data-side=right][data-collapsible=offcanvas]_&]:-left-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInset({ className, ...props }: React.ComponentProps<\"main\">) {\n  return (\n    <main\n      data-slot=\"sidebar-inset\"\n      className={cn(\n        \"bg-background relative flex w-full flex-1 flex-col\",\n        \"md:peer-data-[variant=inset]:m-2 md:peer-data-[variant=inset]:ml-0 md:peer-data-[variant=inset]:rounded-xl md:peer-data-[variant=inset]:shadow-sm md:peer-data-[variant=inset]:peer-data-[state=collapsed]:ml-2\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarInput({\n  className,\n  ...props\n}: React.ComponentProps<typeof Input>) {\n  return (\n    <Input\n      data-slot=\"sidebar-input\"\n      data-sidebar=\"input\"\n      className={cn(\"bg-background h-8 w-full shadow-none\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-header\"\n      data-sidebar=\"header\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-footer\"\n      data-sidebar=\"footer\"\n      className={cn(\"flex flex-col gap-2 p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof Separator>) {\n  return (\n    <Separator\n      data-slot=\"sidebar-separator\"\n      data-sidebar=\"separator\"\n      className={cn(\"bg-sidebar-border mx-2 w-auto\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-content\"\n      data-sidebar=\"content\"\n      className={cn(\n        \"flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroup({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group\"\n      data-sidebar=\"group\"\n      className={cn(\"relative flex w-full min-w-0 flex-col p-2\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupLabel({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"div\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"div\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-label\"\n      data-sidebar=\"group-label\"\n      className={cn(\n        \"text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:h-4 [&>svg]:w-4 [&>svg]:shrink-0\",\n        \"group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupAction({\n  className,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-group-action\"\n      data-sidebar=\"group-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground absolute top-3.5 right-3 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarGroupContent({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-group-content\"\n      data-sidebar=\"group-content\"\n      className={cn(\"w-full text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenu({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu\"\n      data-sidebar=\"menu\"\n      className={cn(\"flex w-full min-w-0 flex-col gap-1\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuItem({ className, ...props }: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-item\"\n      data-sidebar=\"menu-item\"\n      className={cn(\"group/menu-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst sidebarMenuButtonVariants = cva(\n  \"peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:w-8! group-data-[collapsible=icon]:h-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground\",\n        outline:\n          \"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]\",\n      },\n      size: {\n        default: \"h-8 text-sm\",\n        sm: \"h-7 text-xs\",\n        lg: \"h-12 text-sm group-data-[collapsible=icon]:p-0!\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction SidebarMenuButton({\n  asChild = false,\n  isActive = false,\n  variant = \"default\",\n  size = \"default\",\n  tooltip,\n  className,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  isActive?: boolean\n  tooltip?: string | React.ComponentProps<typeof TooltipContent>\n} & VariantProps<typeof sidebarMenuButtonVariants>) {\n  const Comp = asChild ? Slot : \"button\"\n  const { isMobile, state } = useSidebar()\n\n  const button = (\n    <Comp\n      data-slot=\"sidebar-menu-button\"\n      data-sidebar=\"menu-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(sidebarMenuButtonVariants({ variant, size }), className)}\n      {...props}\n    />\n  )\n\n  if (!tooltip) {\n    return button\n  }\n\n  if (typeof tooltip === \"string\") {\n    tooltip = {\n      children: tooltip,\n    }\n  }\n\n  return (\n    <Tooltip>\n      <TooltipTrigger asChild>{button}</TooltipTrigger>\n      <TooltipContent\n        side=\"right\"\n        align=\"center\"\n        hidden={state !== \"collapsed\" || isMobile}\n        {...tooltip}\n      />\n    </Tooltip>\n  )\n}\n\nfunction SidebarMenuAction({\n  className,\n  asChild = false,\n  showOnHover = false,\n  ...props\n}: React.ComponentProps<\"button\"> & {\n  asChild?: boolean\n  showOnHover?: boolean\n}) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-action\"\n      data-sidebar=\"menu-action\"\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground peer-hover/menu-button:text-sidebar-accent-foreground absolute top-1.5 right-1 flex aspect-square w-5 items-center justify-center rounded-md p-0 outline-hidden transition-transform focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0\",\n        // Increases the hit area of the button on mobile.\n        \"after:absolute after:-inset-2 md:after:hidden\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        showOnHover &&\n          \"peer-data-[active=true]/menu-button:text-sidebar-accent-foreground group-focus-within/menu-item:opacity-100 group-hover/menu-item:opacity-100 data-[state=open]:opacity-100 md:opacity-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuBadge({\n  className,\n  ...props\n}: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sidebar-menu-badge\"\n      data-sidebar=\"menu-badge\"\n      className={cn(\n        \"text-sidebar-foreground pointer-events-none absolute right-1 flex h-5 min-w-5 items-center justify-center rounded-md px-1 text-xs font-medium tabular-nums select-none\",\n        \"peer-hover/menu-button:text-sidebar-accent-foreground peer-data-[active=true]/menu-button:text-sidebar-accent-foreground\",\n        \"peer-data-[size=sm]/menu-button:top-1\",\n        \"peer-data-[size=default]/menu-button:top-1.5\",\n        \"peer-data-[size=lg]/menu-button:top-2.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSkeleton({\n  className,\n  showIcon = false,\n  ...props\n}: React.ComponentProps<\"div\"> & {\n  showIcon?: boolean\n}) {\n  // Random width between 50 to 90%.\n  const width = React.useMemo(() => {\n    return `${Math.floor(Math.random() * 40) + 50}%`\n  }, [])\n\n  return (\n    <div\n      data-slot=\"sidebar-menu-skeleton\"\n      data-sidebar=\"menu-skeleton\"\n      className={cn(\"flex h-8 items-center gap-2 rounded-md px-2\", className)}\n      {...props}\n    >\n      {showIcon && (\n        <Skeleton\n          className=\"size-4 rounded-md\"\n          data-sidebar=\"menu-skeleton-icon\"\n        />\n      )}\n      <Skeleton\n        className=\"h-4 max-w-[var(--skeleton-width)] flex-1\"\n        data-sidebar=\"menu-skeleton-text\"\n        style={\n          {\n            \"--skeleton-width\": width,\n          } as React.CSSProperties\n        }\n      />\n    </div>\n  )\n}\n\nfunction SidebarMenuSub({ className, ...props }: React.ComponentProps<\"ul\">) {\n  return (\n    <ul\n      data-slot=\"sidebar-menu-sub\"\n      data-sidebar=\"menu-sub\"\n      className={cn(\n        \"border-sidebar-border mx-3.5 flex min-w-0 translate-x-px flex-col gap-1 border-l px-2.5 py-0.5\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubItem({\n  className,\n  ...props\n}: React.ComponentProps<\"li\">) {\n  return (\n    <li\n      data-slot=\"sidebar-menu-sub-item\"\n      data-sidebar=\"menu-sub-item\"\n      className={cn(\"group/menu-sub-item relative\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SidebarMenuSubButton({\n  asChild = false,\n  size = \"md\",\n  isActive = false,\n  className,\n  ...props\n}: React.ComponentProps<\"a\"> & {\n  asChild?: boolean\n  size?: \"sm\" | \"md\"\n  isActive?: boolean\n}) {\n  const Comp = asChild ? Slot : \"a\"\n\n  return (\n    <Comp\n      data-slot=\"sidebar-menu-sub-button\"\n      data-sidebar=\"menu-sub-button\"\n      data-size={size}\n      data-active={isActive}\n      className={cn(\n        \"text-sidebar-foreground ring-sidebar-ring hover:bg-sidebar-accent hover:text-sidebar-accent-foreground active:bg-sidebar-accent active:text-sidebar-accent-foreground [&>svg]:text-sidebar-accent-foreground flex h-7 min-w-0 -translate-x-px items-center gap-2 overflow-hidden rounded-md px-2 outline outline-2 outline-transparent outline-offset-2 focus-visible:ring-2 disabled:pointer-events-none disabled:opacity-50 aria-disabled:pointer-events-none aria-disabled:opacity-50 [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0\",\n        \"data-[active=true]:bg-sidebar-accent data-[active=true]:text-sidebar-accent-foreground\",\n        size === \"sm\" && \"text-xs\",\n        size === \"md\" && \"text-sm\",\n        \"group-data-[collapsible=icon]:hidden\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sidebar,\n  SidebarContent,\n  SidebarFooter,\n  SidebarGroup,\n  SidebarGroupAction,\n  SidebarGroupContent,\n  SidebarGroupLabel,\n  SidebarHeader,\n  SidebarInput,\n  SidebarInset,\n  SidebarMenu,\n  SidebarMenuAction,\n  SidebarMenuBadge,\n  SidebarMenuButton,\n  SidebarMenuItem,\n  SidebarMenuSkeleton,\n  SidebarMenuSub,\n  SidebarMenuSubButton,\n  SidebarMenuSubItem,\n  SidebarProvider,\n  SidebarRail,\n  SidebarSeparator,\n  SidebarTrigger,\n  useSidebar,\n}\n", "size_bytes": 21846}, "client/src/components/ui/skeleton.tsx": {"content": "import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n", "size_bytes": 261}, "client/src/components/ui/slider.tsx": {"content": "import * as React from \"react\"\nimport * as SliderPrimitive from \"@radix-ui/react-slider\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Slider = React.forwardRef<\n  React.ElementRef<typeof SliderPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof SliderPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <SliderPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex w-full touch-none select-none items-center\",\n      className\n    )}\n    {...props}\n  >\n    <SliderPrimitive.Track className=\"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\">\n      <SliderPrimitive.Range className=\"absolute h-full bg-primary\" />\n    </SliderPrimitive.Track>\n    <SliderPrimitive.Thumb className=\"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\" />\n  </SliderPrimitive.Root>\n))\nSlider.displayName = SliderPrimitive.Root.displayName\n\nexport { Slider }\n", "size_bytes": 1077}, "client/src/components/ui/switch.tsx": {"content": "import * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\"\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n", "size_bytes": 1139}, "client/src/components/ui/table.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n", "size_bytes": 2765}, "client/src/components/ui/tabs.tsx": {"content": "import * as React from \"react\"\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Tabs = TabsPrimitive.Root\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = TabsPrimitive.List.displayName\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsContent.displayName = TabsPrimitive.Content.displayName\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n", "size_bytes": 1883}, "client/src/components/ui/textarea.tsx": {"content": "import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Textarea = React.forwardRef<\n  HTMLTextAreaElement,\n  React.ComponentProps<\"textarea\">\n>(({ className, ...props }, ref) => {\n  return (\n    <textarea\n      className={cn(\n        \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      ref={ref}\n      {...props}\n    />\n  )\n})\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n", "size_bytes": 689}, "client/src/components/ui/toast.tsx": {"content": "import * as React from \"react\"\nimport * as ToastPrimitives from \"@radix-ui/react-toast\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { X } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ToastProvider = ToastPrimitives.Provider\n\nconst ToastViewport = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Viewport>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Viewport\n    ref={ref}\n    className={cn(\n      \"fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]\",\n      className\n    )}\n    {...props}\n  />\n))\nToastViewport.displayName = ToastPrimitives.Viewport.displayName\n\nconst toastVariants = cva(\n  \"group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full\",\n  {\n    variants: {\n      variant: {\n        default: \"border bg-background text-foreground\",\n        destructive:\n          \"destructive group border-destructive bg-destructive text-destructive-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Toast = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> &\n    VariantProps<typeof toastVariants>\n>(({ className, variant, ...props }, ref) => {\n  return (\n    <ToastPrimitives.Root\n      ref={ref}\n      className={cn(toastVariants({ variant }), className)}\n      {...props}\n    />\n  )\n})\nToast.displayName = ToastPrimitives.Root.displayName\n\nconst ToastAction = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Action>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Action\n    ref={ref}\n    className={cn(\n      \"inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive\",\n      className\n    )}\n    {...props}\n  />\n))\nToastAction.displayName = ToastPrimitives.Action.displayName\n\nconst ToastClose = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Close>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Close\n    ref={ref}\n    className={cn(\n      \"absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600\",\n      className\n    )}\n    toast-close=\"\"\n    {...props}\n  >\n    <X className=\"h-4 w-4\" />\n  </ToastPrimitives.Close>\n))\nToastClose.displayName = ToastPrimitives.Close.displayName\n\nconst ToastTitle = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Title>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Title\n    ref={ref}\n    className={cn(\"text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nToastTitle.displayName = ToastPrimitives.Title.displayName\n\nconst ToastDescription = React.forwardRef<\n  React.ElementRef<typeof ToastPrimitives.Description>,\n  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>\n>(({ className, ...props }, ref) => (\n  <ToastPrimitives.Description\n    ref={ref}\n    className={cn(\"text-sm opacity-90\", className)}\n    {...props}\n  />\n))\nToastDescription.displayName = ToastPrimitives.Description.displayName\n\ntype ToastProps = React.ComponentPropsWithoutRef<typeof Toast>\n\ntype ToastActionElement = React.ReactElement<typeof ToastAction>\n\nexport {\n  type ToastProps,\n  type ToastActionElement,\n  ToastProvider,\n  ToastViewport,\n  Toast,\n  ToastTitle,\n  ToastDescription,\n  ToastClose,\n  ToastAction,\n}\n", "size_bytes": 4845}, "client/src/components/ui/toaster.tsx": {"content": "import { useToast } from \"@/hooks/use-toast\"\nimport {\n  Toast,\n  ToastClose,\n  ToastDescription,\n  ToastProvider,\n  ToastTitle,\n  ToastViewport,\n} from \"@/components/ui/toast\"\n\nexport function Toaster() {\n  const { toasts } = useToast()\n\n  return (\n    <ToastProvider>\n      {toasts.map(function ({ id, title, description, action, ...props }) {\n        return (\n          <Toast key={id} {...props}>\n            <div className=\"grid gap-1\">\n              {title && <ToastTitle>{title}</ToastTitle>}\n              {description && (\n                <ToastDescription>{description}</ToastDescription>\n              )}\n            </div>\n            {action}\n            <ToastClose />\n          </Toast>\n        )\n      })}\n      <ToastViewport />\n    </ToastProvider>\n  )\n}\n", "size_bytes": 772}, "client/src/components/ui/toggle-group.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as ToggleGroupPrimitive from \"@radix-ui/react-toggle-group\"\nimport { type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\nimport { toggleVariants } from \"@/components/ui/toggle\"\n\nconst ToggleGroupContext = React.createContext<\n  VariantProps<typeof toggleVariants>\n>({\n  size: \"default\",\n  variant: \"default\",\n})\n\nconst ToggleGroup = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, children, ...props }, ref) => (\n  <ToggleGroupPrimitive.Root\n    ref={ref}\n    className={cn(\"flex items-center justify-center gap-1\", className)}\n    {...props}\n  >\n    <ToggleGroupContext.Provider value={{ variant, size }}>\n      {children}\n    </ToggleGroupContext.Provider>\n  </ToggleGroupPrimitive.Root>\n))\n\nToggleGroup.displayName = ToggleGroupPrimitive.Root.displayName\n\nconst ToggleGroupItem = React.forwardRef<\n  React.ElementRef<typeof ToggleGroupPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof ToggleGroupPrimitive.Item> &\n    VariantProps<typeof toggleVariants>\n>(({ className, children, variant, size, ...props }, ref) => {\n  const context = React.useContext(ToggleGroupContext)\n\n  return (\n    <ToggleGroupPrimitive.Item\n      ref={ref}\n      className={cn(\n        toggleVariants({\n          variant: context.variant || variant,\n          size: context.size || size,\n        }),\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </ToggleGroupPrimitive.Item>\n  )\n})\n\nToggleGroupItem.displayName = ToggleGroupPrimitive.Item.displayName\n\nexport { ToggleGroup, ToggleGroupItem }\n", "size_bytes": 1753}, "client/src/components/ui/toggle.tsx": {"content": "import * as React from \"react\"\nimport * as TogglePrimitive from \"@radix-ui/react-toggle\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst toggleVariants = cva(\n  \"inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors hover:bg-muted hover:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=on]:bg-accent data-[state=on]:text-accent-foreground [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 gap-2\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-transparent\",\n        outline:\n          \"border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n      },\n      size: {\n        default: \"h-10 px-3 min-w-10\",\n        sm: \"h-9 px-2.5 min-w-9\",\n        lg: \"h-11 px-5 min-w-11\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nconst Toggle = React.forwardRef<\n  React.ElementRef<typeof TogglePrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof TogglePrimitive.Root> &\n    VariantProps<typeof toggleVariants>\n>(({ className, variant, size, ...props }, ref) => (\n  <TogglePrimitive.Root\n    ref={ref}\n    className={cn(toggleVariants({ variant, size, className }))}\n    {...props}\n  />\n))\n\nToggle.displayName = TogglePrimitive.Root.displayName\n\nexport { Toggle, toggleVariants }\n", "size_bytes": 1527}, "client/src/components/ui/tooltip.tsx": {"content": "\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n", "size_bytes": 1209}, "plugins/squad-stats.js": {"content": "import BasePlugin from './base-plugin.js';\nimport axios from 'axios';\n\nexport default class SquadStats extends BasePlugin {\n  static get description() {\n    return 'Squad Statistics Dashboard Plugin - Sends game events to external dashboard API for real-time monitoring and statistics';\n  }\n\n  static get defaultEnabled() {\n    return false;\n  }\n\n  static get optionsSpecification() {\n    return {\n      apiBaseUrl: {\n        required: true,\n        description: 'Base URL of the dashboard API (e.g., https://your-dashboard-url)'\n      },\n      apiTimeout: {\n        required: false,\n        description: 'API request timeout in milliseconds',\n        default: 5000\n      },\n      serverId: {\n        required: false,\n        description: 'Unique server identifier for multi-server deployments',\n        default: 1\n      },\n      enableDebugLogging: {\n        required: false,\n        description: 'Enable debug logging for troubleshooting',\n        default: false\n      },\n      retryAttempts: {\n        required: false,\n        description: 'Number of retry attempts for failed API calls',\n        default: 3\n      }\n    };\n  }\n\n  constructor(server, options, connectors) {\n    super(server, options, connectors);\n    \n    // HTTP client configuration\n    this.apiClient = axios.create({\n      baseURL: this.options.apiBaseUrl,\n      timeout: this.options.apiTimeout,\n      headers: {\n        'Content-Type': 'application/json',\n        'User-Agent': 'SquadJS-SquadStats/1.0.0'\n      }\n    });\n\n    // Event handler bindings\n    this.onPlayerConnected = this.onPlayerConnected.bind(this);\n    this.onPlayerDisconnected = this.onPlayerDisconnected.bind(this);\n    this.onPlayerDied = this.onPlayerDied.bind(this);\n    this.onPlayerRevived = this.onPlayerRevived.bind(this);\n    this.onPlayerSpawn = this.onPlayerSpawn.bind(this);\n    this.onNewGame = this.onNewGame.bind(this);\n    this.onRoundEnded = this.onRoundEnded.bind(this);\n  }\n\n  async mount() {\n    this.verbose(1, 'Mounting Squad Stats plugin...');\n    \n    // Test API connectivity\n    try {\n      await this.testApiConnection();\n      this.verbose(1, 'Dashboard API connection successful');\n    } catch (error) {\n      this.verbose(1, `Warning: Cannot connect to dashboard API: ${error.message}`);\n    }\n\n    // Register event listeners using correct SquadJS event names\n    this.server.on('PLAYER_CONNECTED', this.onPlayerConnected);\n    this.server.on('PLAYER_DISCONNECTED', this.onPlayerDisconnected);\n    this.server.on('PLAYER_DIED', this.onPlayerDied);\n    this.server.on('PLAYER_REVIVED', this.onPlayerRevived);\n    this.server.on('PLAYER_SPAWN', this.onPlayerSpawn);\n    this.server.on('NEW_GAME', this.onNewGame);\n    this.server.on('ROUND_ENDED', this.onRoundEnded);\n\n    this.verbose(1, 'Squad Stats plugin mounted successfully');\n  }\n\n  async unmount() {\n    this.verbose(1, 'Unmounting Squad Stats plugin...');\n    \n    // Remove event listeners\n    this.server.off('PLAYER_CONNECTED', this.onPlayerConnected);\n    this.server.off('PLAYER_DISCONNECTED', this.onPlayerDisconnected);\n    this.server.off('PLAYER_DIED', this.onPlayerDied);\n    this.server.off('PLAYER_REVIVED', this.onPlayerRevived);\n    this.server.off('PLAYER_SPAWN', this.onPlayerSpawn);\n    this.server.off('NEW_GAME', this.onNewGame);\n    this.server.off('ROUND_ENDED', this.onRoundEnded);\n\n    this.verbose(1, 'Squad Stats plugin unmounted');\n  }\n\n  async testApiConnection() {\n    const response = await this.apiClient.get('/api/server');\n    return response.status === 200;\n  }\n\n  async sendEventToAPI(eventData, attemptNumber = 1) {\n    try {\n      const response = await this.apiClient.post('/api/events', eventData);\n      \n      if (this.options.enableDebugLogging) {\n        this.verbose(3, `Event sent successfully: ${eventData.type} (ID: ${response.data.event?.id})`);\n      }\n      \n      return response.data;\n    } catch (error) {\n      this.verbose(1, `Failed to send event to dashboard API (attempt ${attemptNumber}): ${error.message}`);\n      \n      // Retry logic for failed requests with exponential backoff\n      if (error.response?.status >= 500 && attemptNumber <= this.options.retryAttempts) {\n        const backoffMs = Math.min(1000 * Math.pow(2, attemptNumber - 1), 10000);\n        this.verbose(2, `Retrying API call in ${backoffMs}ms (attempt ${attemptNumber + 1}/${this.options.retryAttempts + 1})`);\n        \n        return new Promise((resolve, reject) => {\n          setTimeout(async () => {\n            try {\n              const result = await this.sendEventToAPI(eventData, attemptNumber + 1);\n              resolve(result);\n            } catch (retryError) {\n              reject(retryError);\n            }\n          }, backoffMs);\n        });\n      }\n      \n      throw error;\n    }\n  }\n\n  // Event Handlers\n  async onPlayerConnected(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'join',\n        actorSteamId: info.player.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.player.currentLayer || 'Unknown',\n          playerName: info.player.name,\n          eosId: info.player.eosID,\n          teamId: info.player.teamID,\n          squadId: info.player.squadID\n        }\n      });\n\n      this.verbose(2, `Player connected: ${info.player.name} (${info.player.steamID})`);\n    } catch (error) {\n      this.verbose(1, `Error handling player connection: ${error.message}`);\n    }\n  }\n\n  async onPlayerDisconnected(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'leave',\n        actorSteamId: info.player.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.player.currentLayer || 'Unknown',\n          playerName: info.player.name,\n          eosId: info.player.eosID\n        }\n      });\n\n      this.verbose(2, `Player disconnected: ${info.player.name} (${info.player.steamID})`);\n    } catch (error) {\n      this.verbose(1, `Error handling player disconnection: ${error.message}`);\n    }\n  }\n\n  async onPlayerDied(info) {\n    try {\n      // Handle both regular kills and suicides/teamkills\n      const attackerSteamId = info.attacker?.steamID || null;\n      const victimSteamId = info.victim?.steamID;\n\n      await this.sendEventToAPI({\n        type: 'kill',\n        actorSteamId: attackerSteamId,\n        targetSteamId: victimSteamId,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.victim?.currentLayer || 'Unknown',\n          weapon: info.weapon || 'Unknown',\n          headshot: info.headshot || false,\n          killerName: info.attacker?.name || null,\n          victimName: info.victim?.name,\n          killerEOS: info.attacker?.eosID || null,\n          victimEOS: info.victim?.eosID,\n          teamkill: info.teamkill || false,\n          suicide: !info.attacker || info.attacker.steamID === info.victim.steamID\n        }\n      });\n\n      const logMessage = attackerSteamId \n        ? `Kill: ${info.attacker?.name} -> ${info.victim?.name} (${info.weapon})`\n        : `Death: ${info.victim?.name} (suicide/environment)`;\n      \n      this.verbose(2, logMessage);\n    } catch (error) {\n      this.verbose(1, `Error handling player death: ${error.message}`);\n    }\n  }\n\n  async onPlayerRevived(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'revive',\n        actorSteamId: info.reviver?.steamID,\n        targetSteamId: info.victim?.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.victim?.currentLayer || 'Unknown',\n          reviverName: info.reviver?.name,\n          victimName: info.victim?.name,\n          reviverEOS: info.reviver?.eosID,\n          victimEOS: info.victim?.eosID\n        }\n      });\n\n      this.verbose(2, `Revive: ${info.reviver?.name} revived ${info.victim?.name}`);\n    } catch (error) {\n      this.verbose(1, `Error handling player revive: ${error.message}`);\n    }\n  }\n\n  async onPlayerSpawn(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'respawn',\n        actorSteamId: info.player?.steamID,\n        serverId: this.options.serverId,\n        meta: {\n          map: info.player?.currentLayer || 'Unknown',\n          playerName: info.player?.name,\n          eosId: info.player?.eosID,\n          teamId: info.player?.teamID,\n          squadId: info.player?.squadID,\n          role: info.player?.role\n        }\n      });\n\n      this.verbose(3, `Player spawned: ${info.player?.name}`);\n    } catch (error) {\n      this.verbose(1, `Error handling player spawn: ${error.message}`);\n    }\n  }\n\n  async onNewGame(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'round_start',\n        serverId: this.options.serverId,\n        meta: {\n          map: info.layer || info.currentLayer || 'Unknown',\n          gamemode: info.gamemode || 'Unknown'\n        }\n      });\n\n      this.verbose(2, `Round started: ${info.layer || 'Unknown'}`);\n    } catch (error) {\n      this.verbose(1, `Error handling round start: ${error.message}`);\n    }\n  }\n\n  async onRoundEnded(info) {\n    try {\n      await this.sendEventToAPI({\n        type: 'round_end',\n        serverId: this.options.serverId,\n        meta: {\n          map: info.layer || info.currentLayer || 'Unknown',\n          winner: info.winner || 'Unknown',\n          duration: info.duration || 0\n        }\n      });\n\n      this.verbose(2, `Round ended: ${info.layer || 'Unknown'} - Winner: ${info.winner || 'Unknown'}`);\n    } catch (error) {\n      this.verbose(1, `Error handling round end: ${error.message}`);\n    }\n  }\n\n  verbose(level, message) {\n    if (this.server.logger) {\n      this.server.logger(level, 'SquadStats', message);\n    } else {\n      console.log(`[SquadStats] ${message}`);\n    }\n  }\n}", "size_bytes": 9668}}, "version": 1}