# Design Guidelines for SquadJS Plugin Dashboard

## Design Approach
**System-Based Approach**: Using a modified **Carbon Design System** approach, optimized for data-heavy gaming applications with real-time monitoring capabilities. This dashboard prioritizes functionality and information density while maintaining a modern gaming aesthetic.

## Core Design Principles
- **Information Density**: Maximize data visibility without overwhelming users
- **Real-time Focus**: Design for live updates and streaming data
- **Gaming Context**: Reflect the tactical, competitive nature of Squad gameplay
- **Operational Efficiency**: Enable quick decision-making for server administrators

## Color Palette

### Dark Mode (Primary)
- **Background**: 220 15% 8% (primary background)
- **Surface**: 220 12% 12% (cards, panels)
- **Surface Elevated**: 220 10% 16% (modals, dropdowns)
- **Primary**: 200 100% 60% (accent actions, highlights)
- **Success**: 120 60% 50% (positive stats, online status)
- **Warning**: 45 100% 60% (caution indicators)
- **Danger**: 0 70% 55% (kills, critical alerts)
- **Text Primary**: 220 15% 95%
- **Text Secondary**: 220 10% 70%

### Light Mode (Secondary)
- **Background**: 220 20% 97%
- **Surface**: 220 15% 100%
- **Primary**: 200 90% 45%
- **Text Primary**: 220 15% 15%

## Typography
- **Primary Font**: Inter (Google Fonts) - excellent for data tables and UI
- **Monospace Font**: JetBrains Mono (Google Fonts) - for player IDs, timestamps, and technical data
- **Scale**: text-xs, text-sm, text-base, text-lg, text-xl for clear hierarchy

## Layout System
**Tailwind Spacing Units**: Consistent use of 2, 4, 6, 8, 12, 16 units
- Tight spacing: p-2, m-2 for compact data displays
- Standard spacing: p-4, gap-4 for general layout
- Section spacing: p-6, mb-8 for major content areas
- Page spacing: p-8, gap-12 for top-level organization

## Component Library

### Navigation
- **Sidebar Navigation**: Fixed left sidebar with server status indicator
- **Top Bar**: Breadcrumbs, real-time server info, user account dropdown
- **Tab Navigation**: For switching between Leaderboards, Events, Players

### Data Display
- **Stats Cards**: Compact metric displays with trend indicators
- **Data Tables**: Sortable, filterable tables with pagination for player lists and events
- **Real-time Feeds**: Live event streams with auto-scroll and filtering
- **Leaderboards**: Ranked lists with player avatars and stats

### Forms & Controls
- **Filter Panels**: Collapsible sidebar filters for time ranges and event types
- **Search Bars**: Instant search for players and events
- **Date Pickers**: For historical data analysis
- **Toggle Switches**: For real-time updates and notification preferences

### Status Indicators
- **Server Health**: Color-coded status badges (online/offline/high ping)
- **Player Status**: Online/offline indicators with last seen timestamps
- **Connection Quality**: Real-time latency and connection indicators

### Gaming-Specific Elements
- **Kill Feed**: Styled like in-game kill notifications
- **Player Cards**: Steam profile integration with stats overlay
- **Map Indicators**: Server map rotation display
- **Squad Formation**: Visual representation of team compositions

## Images
No large hero images needed. Focus on:
- **Player Avatars**: Steam profile pictures in circular format
- **Map Thumbnails**: Small preview images for current server maps
- **Team/Squad Icons**: Small iconography for team identification
- **Status Icons**: System health and connection indicators

## Responsive Design
- **Desktop First**: Primary focus on admin dashboard experience
- **Tablet Support**: Condensed sidebar, stacked stats cards
- **Mobile Minimal**: Essential monitoring only, simplified navigation

## Real-time Features
- **Live Indicators**: Pulsing dots for active connections
- **Update Animations**: Subtle highlight flashes for new data
- **Connection Status**: Always-visible WebSocket connection indicator
- **Auto-refresh**: Configurable refresh rates for different data types

This design emphasizes operational efficiency and data clarity while maintaining the tactical, professional aesthetic appropriate for gaming server administration.